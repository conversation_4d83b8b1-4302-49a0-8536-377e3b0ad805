--cpu=Cortex-M4.fp
"gd32_xifeng\startup_stm32f429xx.o"
"gd32_xifeng\main.o"
"gd32_xifeng\gpio.o"
"gd32_xifeng\adc.o"
"gd32_xifeng\dac.o"
"gd32_xifeng\dma.o"
"gd32_xifeng\i2c.o"
"gd32_xifeng\spi.o"
"gd32_xifeng\tim.o"
"gd32_xifeng\usart.o"
"gd32_xifeng\stm32f4xx_it.o"
"gd32_xifeng\stm32f4xx_hal_msp.o"
"gd32_xifeng\stm32f4xx_hal_adc.o"
"gd32_xifeng\stm32f4xx_hal_adc_ex.o"
"gd32_xifeng\stm32f4xx_ll_adc.o"
"gd32_xifeng\stm32f4xx_hal_rcc.o"
"gd32_xifeng\stm32f4xx_hal_rcc_ex.o"
"gd32_xifeng\stm32f4xx_hal_flash.o"
"gd32_xifeng\stm32f4xx_hal_flash_ex.o"
"gd32_xifeng\stm32f4xx_hal_flash_ramfunc.o"
"gd32_xifeng\stm32f4xx_hal_gpio.o"
"gd32_xifeng\stm32f4xx_hal_dma_ex.o"
"gd32_xifeng\stm32f4xx_hal_dma.o"
"gd32_xifeng\stm32f4xx_hal_pwr.o"
"gd32_xifeng\stm32f4xx_hal_pwr_ex.o"
"gd32_xifeng\stm32f4xx_hal_cortex.o"
"gd32_xifeng\stm32f4xx_hal.o"
"gd32_xifeng\stm32f4xx_hal_exti.o"
"gd32_xifeng\stm32f4xx_hal_dac.o"
"gd32_xifeng\stm32f4xx_hal_dac_ex.o"
"gd32_xifeng\stm32f4xx_hal_i2c.o"
"gd32_xifeng\stm32f4xx_hal_i2c_ex.o"
"gd32_xifeng\stm32f4xx_hal_spi.o"
"gd32_xifeng\stm32f4xx_hal_tim.o"
"gd32_xifeng\stm32f4xx_hal_tim_ex.o"
"gd32_xifeng\stm32f4xx_hal_uart.o"
"gd32_xifeng\system_stm32f4xx.o"
"gd32_xifeng\gd25qxx.o"
"gd32_xifeng\lfs.o"
"gd32_xifeng\lfs_port.o"
"gd32_xifeng\lfs_util.o"
"gd32_xifeng\ebtn.o"
"gd32_xifeng\oled.o"
"gd32_xifeng\led_app.o"
"gd32_xifeng\scheduler.o"
"gd32_xifeng\btn_app.o"
"gd32_xifeng\usart_app.o"
"gd32_xifeng\adc_app.o"
"gd32_xifeng\app_oled.o"
--library_type=microlib --strict --scatter "GD32_Xifeng\GD32_Xifeng.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "GD32_Xifeng.map" -o GD32_Xifeng\GD32_Xifeng.axf