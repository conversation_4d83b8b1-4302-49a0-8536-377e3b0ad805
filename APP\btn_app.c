#include "btn_app.h"
#include "ebtn.h"
#include "gpio.h"

//TEST
extern uint8_t ucLed[6];

typedef enum
{
    USER_BUTTON_0 = 0,
    USER_BUTTON_1,
    USER_BUTTON_2,
    USER_BUTTON_3,
    USER_BUTTON_4,
    USER_BUTTON_5,
    USER_BUTTON_MAX,

//    USER_BUTTON_COMBO_0 = 0x100,
//    USER_BUTTON_COMBO_1,
//    USER_BUTTON_COMBO_2,
//    USER_BUTTON_COMBO_3,
//    USER_BUTTON_COMBO_MAX,
} user_button_t;

/*  Debounce time in milliseconds, Debounce time in milliseconds for release event, Minimum pressed time for valid click event, Maximum ..., 
    Maximum time between 2 clicks to be considered consecutive click, Time in ms for periodic keep alive event, Max number of consecutive clicks */
static const ebtn_btn_param_t defaul_ebtn_param = EBTN_PARAMS_INIT(20, 0, 20, 1000, 0, 1000, 10);

static ebtn_btn_t btns[] = {
    EBTN_BUTTON_INIT(USER_BUTTON_0, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_1, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_2, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_3, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_4, &defaul_ebtn_param),
    EBTN_BUTTON_INIT(USER_BUTTON_5, &defaul_ebtn_param),
};

//static ebtn_btn_combo_t btns_combo[] = {
//    EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_0, &defaul_ebtn_param, EBTN_EVT_MASK_ONCLICK),
//    EBTN_BUTTON_COMBO_INIT_RAW(USER_BUTTON_COMBO_1, &defaul_ebtn_param, EBTN_EVT_MASK_ONCLICK),
//};

uint8_t prv_btn_get_state(struct ebtn_btn *btn)
{
    switch(btn->key_id) {
        case USER_BUTTON_0:
            return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_15);
        case USER_BUTTON_1:
            return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_13);
        case USER_BUTTON_2:
            return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_11);
        case USER_BUTTON_3:
            return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_9);
        case USER_BUTTON_4:
            return !HAL_GPIO_ReadPin(GPIOE, GPIO_PIN_7);
        case USER_BUTTON_5:
            return !HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_0);
        default:
            return 0;
    }
}

void prv_btn_event(struct ebtn_btn *btn, ebtn_evt_t evt)
{
    if((btn->key_id == USER_BUTTON_0) && (ebtn_click_get_count(btn) == 1)) {
        ucLed[0] ^= 1;
    }
    
    if((btn->key_id == USER_BUTTON_1) && (ebtn_click_get_count(btn) == 1)) {
        ucLed[1] ^= 1;
    }
    
    if((btn->key_id == USER_BUTTON_2) && (ebtn_click_get_count(btn) == 1)) {
        ucLed[2] ^= 1;
    }
    
    if((btn->key_id == USER_BUTTON_3) && (ebtn_click_get_count(btn) == 1)) {
        ucLed[3] ^= 1;
    }
    
    if((btn->key_id == USER_BUTTON_4) && (ebtn_click_get_count(btn) == 1)) {
        ucLed[4] ^= 1;
    }
    
    if((btn->key_id == USER_BUTTON_5) && (ebtn_click_get_count(btn) == 1)) {
        ucLed[5] ^= 1;
    }
    
//    if(btn->key_id == USER_BUTTON_COMBO_0) {
//        rlog_str("btn combo click evt");
//    }
}

void app_btn_init(void)
{
    //ebtn_init(btns, EBTN_ARRAY_SIZE(btns), btns_combo, EBTN_ARRAY_SIZE(btns_combo), prv_btn_get_state, prv_btn_event);
    ebtn_init(btns, EBTN_ARRAY_SIZE(btns), NULL, 0, prv_btn_get_state, prv_btn_event);
    
//    ebtn_combo_btn_add_btn(&btns_combo[0], USER_BUTTON_0);
//    ebtn_combo_btn_add_btn(&btns_combo[0], USER_BUTTON_1);

//    ebtn_combo_btn_add_btn(&btns_combo[1], USER_BUTTON_2);
//    ebtn_combo_btn_add_btn(&btns_combo[1], USER_BUTTON_3);
}

void btn_task(void)
{
    ebtn_process(uwTick);
}

