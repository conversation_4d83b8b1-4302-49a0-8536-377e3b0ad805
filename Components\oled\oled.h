#ifndef __OLED_H
#define __OLED_H

#include "main.h"
#include "i2c.h"
#include <string.h>

// OLED屏幕参数定义
#define OLED_WIDTH  128
#define OLED_HEIGHT 32
#define OLED_PAGE_NUM 4  // 32/8 = 4页

// OLED I2C地址
#define OLED_ADDRESS 0x78

// 字体大小定义
#define FONT_SIZE_8  8
#define FONT_SIZE_16 16

// 显示位置定义
#define COLUMN0 0
#define LINE0   0
#define LINE1   1
#define LINE2   2
#define LINE3   3

// 画笔类型定义
typedef enum {
    PEN_CLEAR = 0x00,
    PEN_WRITE = 0x01,
    PEN_INVERSION = 0x02
} pen_typedef;

// 函数声明
void OLED_Init(void);
void OLED_Clear(void);
void OLED_Refresh_GRAM(void);
void OLED_ShowChar(uint8_t x,uint8_t y,uint8_t chr,uint8_t size,uint8_t mode);
void OLED_ShowString(uint8_t x,uint8_t y,const uint8_t *p);
void OLED_ShowNum(uint8_t x, uint8_t y, uint32_t num, uint8_t len, uint8_t size);
void OLED_ShowFloat(uint8_t x, uint8_t y, float num, uint8_t accuracy, uint8_t size);
void OLED_Display_On(void);
void OLED_Display_Off(void);
void OLED_operate_gram(pen_typedef pen);
void LcdSprintf(uint8_t x, uint8_t y, const char *format, ...);

// DMA传输回调函数声明
void HAL_I2C_MasterTxCpltCallback(I2C_HandleTypeDef *hi2c);
void HAL_I2C_MemTxCpltCallback(I2C_HandleTypeDef *hi2c);

#endif

