<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [GD32_Xifeng\GD32_Xifeng.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image GD32_Xifeng\GD32_Xifeng.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Thu May 08 21:34:39 2025
<BR><P>
<H3>Maximum Stack Usage =       1128 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; lfs_basic_test &rArr; lfs_file_open &rArr; lfs_file_opencfg &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_commit &rArr; lfs_dir_compact &rArr; lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[23]">CAN1_RX0_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[23]">CAN1_RX0_IRQHandler</a><BR>
 <LI><a href="#[9]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">BusFault_Handler</a><BR>
 <LI><a href="#[7]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">HardFault_Handler</a><BR>
 <LI><a href="#[8]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">MemManage_Handler</a><BR>
 <LI><a href="#[6]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">NMI_Handler</a><BR>
 <LI><a href="#[e2]">UART_EndTxTransfer</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[e2]">UART_EndTxTransfer</a><BR>
 <LI><a href="#[e3]">UART_EndRxTransfer</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[e3]">UART_EndRxTransfer</a><BR>
 <LI><a href="#[a]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[a]">UsageFault_Handler</a><BR>
 <LI><a href="#[13d]">lfs_file_write</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[13d]">lfs_file_write</a><BR>
 <LI><a href="#[13a]">lfs_file_read</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[157]">lfs_file_flush</a><BR>
 <LI><a href="#[15f]">lfs_dir_traverse</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[15f]">lfs_dir_traverse</a><BR>
 <LI><a href="#[15c]">lfs_dir_drop</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[154]">lfs_dir_commit</a><BR>
 <LI><a href="#[16f]">lfs_fs_relocate</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[154]">lfs_dir_commit</a><BR>
 <LI><a href="#[16c]">lfs_dir_split</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[166]">lfs_dir_compact</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[21]">ADC_IRQHandler</a> from stm32f4xx_it.o(i.ADC_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[9]">BusFault_Handler</a> from stm32f4xx_it.o(i.BusFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[23]">CAN1_RX0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[24]">CAN1_RX1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[25]">CAN1_SCE_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[22]">CAN1_TX_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4f]">CAN2_RX0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[50]">CAN2_RX1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[51]">CAN2_SCE_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4e]">CAN2_TX_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5d]">DCMI_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1a]">DMA1_Stream0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1b]">DMA1_Stream1_IRQHandler</a> from stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1c]">DMA1_Stream2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1d]">DMA1_Stream3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1e]">DMA1_Stream4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[1f]">DMA1_Stream5_IRQHandler</a> from stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[20]">DMA1_Stream6_IRQHandler</a> from stm32f4xx_it.o(i.DMA1_Stream6_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3e]">DMA1_Stream7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[68]">DMA2D_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[47]">DMA2_Stream0_IRQHandler</a> from stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[48]">DMA2_Stream1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[49]">DMA2_Stream2_IRQHandler</a> from stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4a]">DMA2_Stream3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4b]">DMA2_Stream4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[53]">DMA2_Stream5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[54]">DMA2_Stream6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[55]">DMA2_Stream7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[c]">DebugMon_Handler</a> from stm32f4xx_it.o(i.DebugMon_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4c]">ETH_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4d]">ETH_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[15]">EXTI0_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[37]">EXTI15_10_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[16]">EXTI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[17]">EXTI2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[18]">EXTI3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[19]">EXTI4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[26]">EXTI9_5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[13]">FLASH_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3f]">FMC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5f]">FPU_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5e]">HASH_RNG_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[7]">HardFault_Handler</a> from stm32f4xx_it.o(i.HardFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2f]">I2C1_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2e]">I2C1_EV_IRQHandler</a> from stm32f4xx_it.o(i.I2C1_EV_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[31]">I2C2_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[30]">I2C2_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[58]">I2C3_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[57]">I2C3_EV_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6f]">I2C_DMAAbort</a> from stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) referenced from stm32f4xx_hal_i2c.o(i.I2C_ITError)
 <LI><a href="#[6f]">I2C_DMAAbort</a> from stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) referenced from stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF)
 <LI><a href="#[6d]">I2C_DMAError</a> from stm32f4xx_hal_i2c.o(i.I2C_DMAError) referenced from stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA)
 <LI><a href="#[6d]">I2C_DMAError</a> from stm32f4xx_hal_i2c.o(i.I2C_DMAError) referenced from stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA)
 <LI><a href="#[6c]">I2C_DMAXferCplt</a> from stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) referenced from stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA)
 <LI><a href="#[6c]">I2C_DMAXferCplt</a> from stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) referenced from stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA)
 <LI><a href="#[67]">LTDC_ER_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[66]">LTDC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[8]">MemManage_Handler</a> from stm32f4xx_it.o(i.MemManage_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6]">NMI_Handler</a> from stm32f4xx_it.o(i.NMI_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[4]">OLED_Proc</a> from app_oled.o(i.OLED_Proc) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[52]">OTG_FS_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[39]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5a]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[59]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5c]">OTG_HS_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5b]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[10]">PVD_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[d]">PendSV_Handler</a> from stm32f4xx_it.o(i.PendSV_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[14]">RCC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[38]">RTC_Alarm_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[12]">RTC_WKUP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[5]">Reset_Handler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[65]">SAI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[40]">SDIO_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[32]">SPI1_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[33]">SPI2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[42]">SPI3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[62]">SPI4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[63]">SPI5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[64]">SPI6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[b]">SVC_Handler</a> from stm32f4xx_it.o(i.SVC_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[e]">SysTick_Handler</a> from stm32f4xx_it.o(i.SysTick_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6a]">SystemInit</a> from system_stm32f4xx.o(i.SystemInit) referenced from startup_stm32f429xx.o(.text)
 <LI><a href="#[11]">TAMP_STAMP_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[27]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2a]">TIM1_CC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[29]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[28]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2b]">TIM2_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2c]">TIM3_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[2d]">TIM4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[41]">TIM5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[45]">TIM6_DAC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[46]">TIM7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3a]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3d]">TIM8_CC_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3c]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[3b]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[43]">UART4_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[44]">UART5_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[60]">UART7_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[61]">UART8_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6e]">UART_DMAAbortOnError</a> from stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) referenced from stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
 <LI><a href="#[72]">UART_DMAError</a> from stm32f4xx_hal_uart.o(i.UART_DMAError) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[70]">UART_DMAReceiveCplt</a> from stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[71]">UART_DMARxHalfCplt</a> from stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) referenced from stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
 <LI><a href="#[34]">USART1_IRQHandler</a> from stm32f4xx_it.o(i.USART1_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[35]">USART2_IRQHandler</a> from stm32f4xx_it.o(i.USART2_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[36]">USART3_IRQHandler</a> from stm32f4xx_it.o(i.USART3_IRQHandler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[56]">USART6_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[a]">UsageFault_Handler</a> from stm32f4xx_it.o(i.UsageFault_Handler) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[f]">WWDG_IRQHandler</a> from startup_stm32f429xx.o(.text) referenced from startup_stm32f429xx.o(RESET)
 <LI><a href="#[6b]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f429xx.o(.text)
 <LI><a href="#[74]">_snputc</a> from printfa.o(i._snputc) referenced from printfa.o(i.__0vsnprintf)
 <LI><a href="#[2]">btn_task</a> from btn_app.o(i.btn_task) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[73]">fputc</a> from usart.o(i.fputc) referenced from printfa.o(i.__0printf)
 <LI><a href="#[1]">led_task</a> from led_app.o(i.led_task) referenced 2 times from scheduler.o(.data)
 <LI><a href="#[77]">lfs_alloc_lookahead</a> from lfs.o(i.lfs_alloc_lookahead) referenced from lfs.o(i.lfs_alloc)
 <LI><a href="#[80]">lfs_deskio_erase</a> from lfs_port.o(i.lfs_deskio_erase) referenced from lfs_port.o(i.lfs_storage_init)
 <LI><a href="#[7f]">lfs_deskio_prog</a> from lfs_port.o(i.lfs_deskio_prog) referenced from lfs_port.o(i.lfs_storage_init)
 <LI><a href="#[7e]">lfs_deskio_read</a> from lfs_port.o(i.lfs_deskio_read) referenced from lfs_port.o(i.lfs_storage_init)
 <LI><a href="#[81]">lfs_deskio_sync</a> from lfs_port.o(i.lfs_deskio_sync) referenced from lfs_port.o(i.lfs_storage_init)
 <LI><a href="#[78]">lfs_dir_commit_commit</a> from lfs.o(i.lfs_dir_commit_commit) referenced from lfs.o(i.lfs_dir_commit)
 <LI><a href="#[78]">lfs_dir_commit_commit</a> from lfs.o(i.lfs_dir_commit_commit) referenced from lfs.o(i.lfs_dir_compact)
 <LI><a href="#[79]">lfs_dir_commit_size</a> from lfs.o(i.lfs_dir_commit_size) referenced from lfs.o(i.lfs_dir_compact)
 <LI><a href="#[7a]">lfs_dir_find_match</a> from lfs.o(i.lfs_dir_find_match) referenced from lfs.o(i.lfs_dir_find)
 <LI><a href="#[7a]">lfs_dir_find_match</a> from lfs.o(i.lfs_dir_find_match) referenced from lfs.o(i.lfs_mount)
 <LI><a href="#[7b]">lfs_dir_traverse_filter</a> from lfs.o(i.lfs_dir_traverse_filter) referenced from lfs.o(i.lfs_dir_traverse)
 <LI><a href="#[7c]">lfs_fs_parent_match</a> from lfs.o(i.lfs_fs_parent_match) referenced from lfs.o(i.lfs_fs_parent)
 <LI><a href="#[7d]">lfs_fs_size_count</a> from lfs.o(i.lfs_fs_size_count) referenced from lfs.o(i.lfs_fs_size)
 <LI><a href="#[69]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[75]">prv_btn_event</a> from btn_app.o(i.prv_btn_event) referenced from btn_app.o(i.app_btn_init)
 <LI><a href="#[76]">prv_btn_get_state</a> from btn_app.o(i.prv_btn_get_state) referenced from btn_app.o(i.app_btn_init)
 <LI><a href="#[3]">uart_task</a> from usart_app.o(i.uart_task) referenced 2 times from scheduler.o(.data)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[6b]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(.text)
</UL>
<P><STRONG><a name="[191]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[82]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[93]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[192]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[193]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[194]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[195]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[196]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[5]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CAN1_RX0_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>DMA2D_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>LTDC_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>LTDC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>SPI6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>TIM4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>UART7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>UART8_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[56]"></a>USART6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[84]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[e1]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[15d]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[197]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[88]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[198]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[199]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[87]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[9e]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_init
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
</UL>

<P><STRONG><a name="[19a]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[89]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[175]"></a>strchr</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, strchr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
</UL>

<P><STRONG><a name="[17d]"></a>strlen</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, strlen.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
</UL>

<P><STRONG><a name="[127]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_cmp
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
</UL>

<P><STRONG><a name="[172]"></a>strspn</STRONG> (Thumb, 28 bytes, Stack size 12 bytes, strspn.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strspn
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
</UL>

<P><STRONG><a name="[173]"></a>strcspn</STRONG> (Thumb, 34 bytes, Stack size 12 bytes, strcspn.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strcspn
</UL>
<BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
</UL>

<P><STRONG><a name="[19b]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[123]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[86]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[19c]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[85]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[19d]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[19e]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 48 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[8a]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[8e]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[8f]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[90]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[91]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[92]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[120]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[83]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[19f]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[8b]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[1a0]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[8d]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[8c]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[1a1]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[1a2]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[21]"></a>ADC_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.ADC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = ADC_IRQHandler &rArr; HAL_ADC_IRQHandler &rArr; HAL_ADC_ConvCpltCallback &rArr; HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.BusFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA1_Stream1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Stream1_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Stream5_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA1_Stream6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA1_Stream6_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA2_Stream0_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DMA2_Stream2_IRQHandler &rArr; HAL_DMA_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.DebugMon_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[a1]"></a>Error_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(i.Error_Handler))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[98]"></a>HAL_ADCEx_InjectedConvCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[100]"></a>HAL_ADC_ConfigChannel</STRONG> (Thumb, 334 bytes, Stack size 20 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = HAL_ADC_ConfigChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[96]"></a>HAL_ADC_ConvCpltCallback</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, adc_app.o(i.HAL_ADC_ConvCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_ADC_ConvCpltCallback &rArr; HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Stop_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[9a]"></a>HAL_ADC_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[94]"></a>HAL_ADC_IRQHandler</STRONG> (Thumb, 302 bytes, Stack size 16 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = HAL_ADC_IRQHandler &rArr; HAL_ADC_ConvCpltCallback &rArr; HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_LevelOutOfWindowCallback
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ErrorCallback
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADCEx_InjectedConvCpltCallback
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>

<P><STRONG><a name="[9b]"></a>HAL_ADC_Init</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
</UL>

<P><STRONG><a name="[99]"></a>HAL_ADC_LevelOutOfWindowCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback))
<BR><BR>[Called By]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_IRQHandler
</UL>

<P><STRONG><a name="[9c]"></a>HAL_ADC_MspInit</STRONG> (Thumb, 174 bytes, Stack size 40 bytes, adc.o(i.HAL_ADC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[97]"></a>HAL_ADC_Stop_DMA</STRONG> (Thumb, 108 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_ADC_Stop_DMA &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
</UL>
<BR>[Called By]<UL><LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConvCpltCallback
</UL>

<P><STRONG><a name="[102]"></a>HAL_DAC_ConfigChannel</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_ConfigChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_DAC_ConfigChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
</UL>

<P><STRONG><a name="[a5]"></a>HAL_DAC_Init</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, stm32f4xx_hal_dac.o(i.HAL_DAC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_DAC_Init &rArr; HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
</UL>

<P><STRONG><a name="[a6]"></a>HAL_DAC_MspInit</STRONG> (Thumb, 84 bytes, Stack size 32 bytes, dac.o(i.HAL_DAC_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Init
</UL>

<P><STRONG><a name="[a4]"></a>HAL_DMA_Abort</STRONG> (Thumb, 146 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Stop_DMA
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
</UL>

<P><STRONG><a name="[c6]"></a>HAL_DMA_Abort_IT</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write_DMA
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_STOPF
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_ITError
</UL>

<P><STRONG><a name="[f2]"></a>HAL_DMA_GetError</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_GetError))
<BR><BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_DMAError
</UL>

<P><STRONG><a name="[fa]"></a>HAL_DMA_GetState</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_GetState))
<BR><BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_STOPF
</UL>

<P><STRONG><a name="[95]"></a>HAL_DMA_IRQHandler</STRONG> (Thumb, 412 bytes, Stack size 32 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_DMA_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream2_IRQHandler
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA2_Stream0_IRQHandler
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream6_IRQHandler
<LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream5_IRQHandler
<LI><a href="#[1b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA1_Stream1_IRQHandler
</UL>

<P><STRONG><a name="[a0]"></a>HAL_DMA_Init</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_DMA_Init &rArr; DMA_CheckFifoParam
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CheckFifoParam
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_CalcBaseAndBitshift
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
</UL>

<P><STRONG><a name="[aa]"></a>HAL_DMA_Start_IT</STRONG> (Thumb, 110 bytes, Stack size 24 bytes, stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write_DMA
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit_DMA
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>

<P><STRONG><a name="[ac]"></a>HAL_Delay</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_Delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_Delay
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_Cmd
</UL>

<P><STRONG><a name="[9f]"></a>HAL_GPIO_Init</STRONG> (Thumb, 510 bytes, Stack size 40 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_MspInit
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
</UL>

<P><STRONG><a name="[18c]"></a>HAL_GPIO_ReadPin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_btn_get_state
</UL>

<P><STRONG><a name="[105]"></a>HAL_GPIO_WritePin</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin))
<BR><BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_disp
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>

<P><STRONG><a name="[a7]"></a>HAL_GetTick</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_GetTick))
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_EnableOverDrive
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write_DMA
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_IsDeviceReady
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>

<P><STRONG><a name="[107]"></a>HAL_I2CEx_ConfigAnalogFilter</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigAnalogFilter))
<BR><BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[108]"></a>HAL_I2CEx_ConfigDigitalFilter</STRONG> (Thumb, 62 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c_ex.o(i.HAL_I2CEx_ConfigDigitalFilter))
<BR><BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[f1]"></a>HAL_I2C_AbortCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_ITError
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_DMAAbort
</UL>

<P><STRONG><a name="[f9]"></a>HAL_I2C_AddrCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback))
<BR><BR>[Called By]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_ADDR
</UL>

<P><STRONG><a name="[ad]"></a>HAL_I2C_EV_IRQHandler</STRONG> (Thumb, 560 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = HAL_I2C_EV_IRQHandler &rArr; I2C_MasterTransmit_TXE &rArr; HAL_I2C_MasterTxCpltCallback &rArr; HAL_I2C_Mem_Write_DMA &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_SlaveTxCpltCallback
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_SlaveRxCpltCallback
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_STOPF
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_ADDR
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MemoryTransmit_TXE_BTF
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Master_SB
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Master_ADDR
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterTransmit_TXE
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterTransmit_BTF
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterReceive_RXNE
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterReceive_BTF
</UL>
<BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C1_EV_IRQHandler
</UL>

<P><STRONG><a name="[f0]"></a>HAL_I2C_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterReceive_RXNE
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_ITError
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_DMAXferCplt
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_DMAError
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_DMAAbort
</UL>

<P><STRONG><a name="[b9]"></a>HAL_I2C_Init</STRONG> (Thumb, 376 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
</UL>
<BR>[Called By]<UL><LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
</UL>

<P><STRONG><a name="[bc]"></a>HAL_I2C_IsDeviceReady</STRONG> (Thumb, 354 bytes, Stack size 56 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_I2C_IsDeviceReady &rArr; I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[f6]"></a>HAL_I2C_ListenCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_STOPF
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_ITError
</UL>

<P><STRONG><a name="[f3]"></a>HAL_I2C_MasterRxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterReceive_RXNE
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterReceive_BTF
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_DMAXferCplt
</UL>

<P><STRONG><a name="[be]"></a>HAL_I2C_MasterTxCpltCallback</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, oled.o(i.HAL_I2C_MasterTxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = HAL_I2C_MasterTxCpltCallback &rArr; HAL_I2C_Mem_Write_DMA &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterTransmit_TXE
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterTransmit_BTF
</UL>

<P><STRONG><a name="[c0]"></a>HAL_I2C_Master_Transmit_DMA</STRONG> (Thumb, 332 bytes, Stack size 24 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = HAL_I2C_Master_Transmit_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh_GRAM
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MemTxCpltCallback
</UL>

<P><STRONG><a name="[f4]"></a>HAL_I2C_MemRxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterReceive_RXNE
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterReceive_BTF
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_DMAXferCplt
</UL>

<P><STRONG><a name="[c1]"></a>HAL_I2C_MemTxCpltCallback</STRONG> (Thumb, 44 bytes, Stack size 0 bytes, oled.o(i.HAL_I2C_MemTxCpltCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = HAL_I2C_MemTxCpltCallback &rArr; HAL_I2C_Master_Transmit_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MemoryTransmit_TXE_BTF
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterTransmit_TXE
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterTransmit_BTF
</UL>

<P><STRONG><a name="[c2]"></a>HAL_I2C_Mem_Write</STRONG> (Thumb, 294 bytes, Stack size 64 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_Cmd
</UL>

<P><STRONG><a name="[bf]"></a>HAL_I2C_Mem_Write_DMA</STRONG> (Thumb, 388 bytes, Stack size 48 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_I2C_Mem_Write_DMA &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MasterTxCpltCallback
</UL>

<P><STRONG><a name="[ba]"></a>HAL_I2C_MspInit</STRONG> (Thumb, 158 bytes, Stack size 40 bytes, i2c.o(i.HAL_I2C_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
</UL>

<P><STRONG><a name="[b7]"></a>HAL_I2C_SlaveRxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_STOPF
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_DMAXferCplt
</UL>

<P><STRONG><a name="[b8]"></a>HAL_I2C_SlaveTxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_DMAXferCplt
</UL>

<P><STRONG><a name="[118]"></a>HAL_IncTick</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_hal.o(i.HAL_IncTick))
<BR><BR>[Called By]<UL><LI><a href="#[e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>

<P><STRONG><a name="[c7]"></a>HAL_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, stm32f4xx_hal.o(i.HAL_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_Init &rArr; HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_MspInit
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriorityGrouping
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[c9]"></a>HAL_InitTick</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f4xx_hal.o(i.HAL_InitTick))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_InitTick &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[ca]"></a>HAL_MspInit</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, stm32f4xx_hal_msp.o(i.HAL_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[a3]"></a>HAL_NVIC_EnableIRQ</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ))
<BR><BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
</UL>

<P><STRONG><a name="[a2]"></a>HAL_NVIC_SetPriority</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MspInit
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_MspInit
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[c8]"></a>HAL_NVIC_SetPriorityGrouping</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping))
<BR><BR>[Called By]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
</UL>

<P><STRONG><a name="[cd]"></a>HAL_PWREx_EnableOverDrive</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableOverDrive))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_PWREx_EnableOverDrive
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[ce]"></a>HAL_RCC_ClockConfig</STRONG> (Thumb, 288 bytes, Stack size 32 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetSysClockFreq
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[bb]"></a>HAL_RCC_GetPCLK1Freq</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq))
<BR><BR>[Called By]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[11b]"></a>HAL_RCC_GetPCLK2Freq</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq))
<BR><BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>

<P><STRONG><a name="[cf]"></a>HAL_RCC_GetSysClockFreq</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
</UL>

<P><STRONG><a name="[d0]"></a>HAL_RCC_OscConfig</STRONG> (Thumb, 856 bytes, Stack size 40 bytes, stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = HAL_RCC_OscConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>

<P><STRONG><a name="[d1]"></a>HAL_SPI_Init</STRONG> (Thumb, 188 bytes, Stack size 16 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
</UL>

<P><STRONG><a name="[d2]"></a>HAL_SPI_MspInit</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, spi.o(i.HAL_SPI_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
</UL>

<P><STRONG><a name="[d3]"></a>HAL_SPI_TransmitReceive</STRONG> (Thumb, 496 bytes, Stack size 56 bytes, stm32f4xx_hal_spi.o(i.HAL_SPI_TransmitReceive))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
</UL>

<P><STRONG><a name="[cb]"></a>HAL_SYSTICK_Config</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_SYSTICK_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_InitTick
</UL>

<P><STRONG><a name="[10b]"></a>HAL_TIMEx_MasterConfigSynchronization</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = HAL_TIMEx_MasterConfigSynchronization
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[d5]"></a>HAL_TIM_Base_Init</STRONG> (Thumb, 90 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_MspInit
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[d6]"></a>HAL_TIM_Base_MspInit</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, tim.o(i.HAL_TIM_Base_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = HAL_TIM_Base_MspInit
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[10c]"></a>HAL_TIM_Base_Start</STRONG> (Thumb, 90 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start))
<BR><BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[d8]"></a>HAL_TIM_ConfigClockSource</STRONG> (Thumb, 220 bytes, Stack size 16 bytes, stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = HAL_TIM_ConfigClockSource &rArr; TIM_TI2_ConfigInputStage
</UL>
<BR>[Calls]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI2_ConfigInputStage
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TI1_ConfigInputStage
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITRx_SetConfig
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETR_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
</UL>

<P><STRONG><a name="[dd]"></a>HAL_UARTEx_ReceiveToIdle_DMA</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[df]"></a>HAL_UARTEx_RxEventCallback</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, usart_app.o(i.HAL_UARTEx_RxEventCallback))
<BR><BR>[Stack]<UL><LI>Max Depth = 76<LI>Call Chain = HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[e0]"></a>HAL_UART_DMAStop</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = HAL_UART_DMAStop &rArr; HAL_DMA_Abort
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>

<P><STRONG><a name="[e6]"></a>HAL_UART_ErrorCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAAbortOnError
</UL>

<P><STRONG><a name="[e4]"></a>HAL_UART_IRQHandler</STRONG> (Thumb, 618 bytes, Stack size 24 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_TxCpltCallback
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
<LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[e8]"></a>HAL_UART_Init</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_MspInit
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
</UL>

<P><STRONG><a name="[e9]"></a>HAL_UART_MspInit</STRONG> (Thumb, 500 bytes, Stack size 56 bytes, usart.o(i.HAL_UART_MspInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[eb]"></a>HAL_UART_Receive_IT</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT))
<BR><BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Start_Receive_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
</UL>

<P><STRONG><a name="[ed]"></a>HAL_UART_RxCpltCallback</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, usart_app.o(i.HAL_UART_RxCpltCallback))
<BR><BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_Receive_IT
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAReceiveCplt
</UL>

<P><STRONG><a name="[11a]"></a>HAL_UART_RxHalfCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMARxHalfCplt
</UL>

<P><STRONG><a name="[ee]"></a>HAL_UART_Transmit</STRONG> (Thumb, 160 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_Transmit))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
</UL>

<P><STRONG><a name="[e7]"></a>HAL_UART_TxCpltCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback))
<BR><BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[7]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.HardFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.I2C1_EV_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = I2C1_EV_IRQHandler &rArr; HAL_I2C_EV_IRQHandler &rArr; I2C_MasterTransmit_TXE &rArr; HAL_I2C_MasterTxCpltCallback &rArr; HAL_I2C_Mem_Write_DMA &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[fc]"></a>LcdSprintf</STRONG> (Thumb, 40 bytes, Stack size 160 bytes, oled.o(i.LcdSprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 244<LI>Call Chain = LcdSprintf &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Proc
</UL>

<P><STRONG><a name="[ff]"></a>MX_ADC1_Init</STRONG> (Thumb, 112 bytes, Stack size 32 bytes, adc.o(i.MX_ADC1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = MX_ADC1_Init &rArr; HAL_ADC_Init &rArr; HAL_ADC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_ConfigChannel
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[101]"></a>MX_DAC_Init</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, dac.o(i.MX_DAC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = MX_DAC_Init &rArr; HAL_DAC_Init &rArr; HAL_DAC_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_Init
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DAC_ConfigChannel
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[103]"></a>MX_DMA_Init</STRONG> (Thumb, 120 bytes, Stack size 8 bytes, dma.o(i.MX_DMA_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = MX_DMA_Init &rArr; HAL_NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[104]"></a>MX_GPIO_Init</STRONG> (Thumb, 252 bytes, Stack size 56 bytes, gpio.o(i.MX_GPIO_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_GPIO_Init &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_Init
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[106]"></a>MX_I2C1_Init</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, i2c.o(i.MX_I2C1_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = MX_I2C1_Init &rArr; HAL_I2C_Init &rArr; HAL_I2C_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Init
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigDigitalFilter
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2CEx_ConfigAnalogFilter
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[109]"></a>MX_SPI2_Init</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, spi.o(i.MX_SPI2_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = MX_SPI2_Init &rArr; HAL_SPI_Init &rArr; HAL_SPI_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10a]"></a>MX_TIM3_Init</STRONG> (Thumb, 100 bytes, Stack size 32 bytes, tim.o(i.MX_TIM3_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = MX_TIM3_Init &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
<LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Start
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10d]"></a>MX_TIM6_Init</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, tim.o(i.MX_TIM6_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = MX_TIM6_Init &rArr; HAL_TIM_Base_Init &rArr; TIM_Base_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIMEx_MasterConfigSynchronization
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10e]"></a>MX_USART1_UART_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, usart.o(i.MX_USART1_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = MX_USART1_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10f]"></a>MX_USART2_UART_Init</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, usart.o(i.MX_USART2_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = MX_USART2_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[110]"></a>MX_USART3_UART_Init</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, usart.o(i.MX_USART3_UART_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = MX_USART3_UART_Init &rArr; HAL_UART_Init &rArr; HAL_UART_MspInit &rArr; HAL_GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Error_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.MemManage_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.NMI_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[113]"></a>OLED_Clear</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, oled.o(i.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[116]"></a>OLED_DrawPoint</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, oled.o(i.OLED_DrawPoint))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = OLED_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[111]"></a>OLED_Init</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = OLED_Init &rArr; OLED_Write_Cmd &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_IsDeviceReady
<LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Write_Cmd
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[4]"></a>OLED_Proc</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, app_oled.o(i.OLED_Proc))
<BR><BR>[Stack]<UL><LI>Max Depth = 252<LI>Call Chain = OLED_Proc &rArr; LcdSprintf &rArr; OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh_GRAM
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LcdSprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[114]"></a>OLED_Refresh_GRAM</STRONG> (Thumb, 80 bytes, Stack size 8 bytes, oled.o(i.OLED_Refresh_GRAM))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = OLED_Refresh_GRAM &rArr; HAL_I2C_Master_Transmit_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Master_Transmit_DMA
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Proc
</UL>

<P><STRONG><a name="[115]"></a>OLED_ShowChar</STRONG> (Thumb, 134 bytes, Stack size 52 bytes, oled.o(i.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_DrawPoint
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
</UL>

<P><STRONG><a name="[fe]"></a>OLED_ShowString</STRONG> (Thumb, 62 bytes, Stack size 24 bytes, oled.o(i.OLED_ShowString))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = OLED_ShowString &rArr; OLED_ShowChar &rArr; OLED_DrawPoint
</UL>
<BR>[Calls]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LcdSprintf
</UL>

<P><STRONG><a name="[d]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.PendSV_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SVC_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>SysTick_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.SysTick_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_IncTick
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[119]"></a>SystemClock_Config</STRONG> (Thumb, 148 bytes, Stack size 88 bytes, main.o(i.SystemClock_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = SystemClock_Config &rArr; HAL_RCC_ClockConfig &rArr; HAL_RCC_GetSysClockFreq &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_OscConfig
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_ClockConfig
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_PWREx_EnableOverDrive
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6a]"></a>SystemInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, system_stm32f4xx.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(.text)
</UL>
<P><STRONG><a name="[d7]"></a>TIM_Base_SetConfig</STRONG> (Thumb, 170 bytes, Stack size 20 bytes, stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TIM_Base_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_Base_Init
</UL>

<P><STRONG><a name="[d9]"></a>TIM_ETR_SetConfig</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_ETR_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[de]"></a>UART_Start_Receive_DMA</STRONG> (Thumb, 130 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>
<BR>[Called By]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
</UL>

<P><STRONG><a name="[ec]"></a>UART_Start_Receive_IT</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT))
<BR><BR>[Called By]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Receive_IT
</UL>

<P><STRONG><a name="[34]"></a>USART1_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.USART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = USART1_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>USART2_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_it.o(i.USART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = USART2_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_ReceiveToIdle_DMA
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>USART3_IRQHandler</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.USART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = USART3_IRQHandler &rArr; HAL_UART_IRQHandler &rArr; UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(i.UsageFault_Handler))
<BR><BR>[Calls]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429xx.o(RESET)
</UL>
<P><STRONG><a name="[11c]"></a>__0printf</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1a3]"></a>__1printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[134]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_demove
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
</UL>

<P><STRONG><a name="[1a4]"></a>__c89printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[1a5]"></a>printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0printf), UNUSED)

<P><STRONG><a name="[11e]"></a>__0vsnprintf</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[1a6]"></a>__1vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[1a7]"></a>__2vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[1a8]"></a>__c89vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf), UNUSED)

<P><STRONG><a name="[fd]"></a>vsnprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0vsnprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LcdSprintf
</UL>

<P><STRONG><a name="[1a9]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[1aa]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[1ab]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[124]"></a>app_btn_init</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, btn_app.o(i.app_btn_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = app_btn_init &rArr; ebtn_init
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_init
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[2]"></a>btn_task</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, btn_app.o(i.btn_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = btn_task &rArr; ebtn_process &rArr; ebtn_process_with_curr_state &rArr; ebtn_process_btn_combo &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[125]"></a>ebtn_init</STRONG> (Thumb, 58 bytes, Stack size 32 bytes, ebtn.o(i.ebtn_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ebtn_init
</UL>
<BR>[Calls]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_btn_init
</UL>

<P><STRONG><a name="[128]"></a>ebtn_process</STRONG> (Thumb, 94 bytes, Stack size 32 bytes, ebtn.o(i.ebtn_process))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = ebtn_process &rArr; ebtn_process_with_curr_state &rArr; ebtn_process_btn_combo &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_assign
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;btn_task
</UL>

<P><STRONG><a name="[12a]"></a>ebtn_process_with_curr_state</STRONG> (Thumb, 174 bytes, Stack size 32 bytes, ebtn.o(i.ebtn_process_with_curr_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = ebtn_process_with_curr_state &rArr; ebtn_process_btn_combo &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>

<P><STRONG><a name="[73]"></a>fputc</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, usart.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = fputc &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0printf)
</UL>
<P><STRONG><a name="[14f]"></a>free</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, malloc.o(i.free))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = free
</UL>
<BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_close
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_deinit
</UL>

<P><STRONG><a name="[130]"></a>led_disp</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, led_app.o(i.led_disp))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = led_disp
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_task
</UL>

<P><STRONG><a name="[1]"></a>led_task</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, led_app.o(i.led_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = led_task &rArr; led_disp
</UL>
<BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;led_disp
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL>
<P><STRONG><a name="[135]"></a>lfs_basic_test</STRONG> (Thumb, 322 bytes, Stack size 112 bytes, main.o(i.lfs_basic_test))
<BR><BR>[Stack]<UL><LI>Max Depth = 1128<LI>Call Chain = lfs_basic_test &rArr; lfs_file_open &rArr; lfs_file_opencfg &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_commit &rArr; lfs_dir_compact &rArr; lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_rewind
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_open
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_close
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[16b]"></a>lfs_crc</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, lfs_util.o(i.lfs_crc))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_crc
</UL>
<BR>[Called By]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitprog
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
</UL>

<P><STRONG><a name="[13c]"></a>lfs_file_close</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, lfs.o(i.lfs_file_close))
<BR><BR>[Stack]<UL><LI>Max Depth = 832<LI>Call Chain = lfs_file_close &rArr; lfs_file_sync &rArr; lfs_dir_commit &rArr; lfs_dir_compact &rArr; lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_sync
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
</UL>

<P><STRONG><a name="[139]"></a>lfs_file_open</STRONG> (Thumb, 12 bytes, Stack size 16 bytes, lfs.o(i.lfs_file_open))
<BR><BR>[Stack]<UL><LI>Max Depth = 1016<LI>Call Chain = lfs_file_open &rArr; lfs_file_opencfg &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_commit &rArr; lfs_dir_compact &rArr; lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
</UL>

<P><STRONG><a name="[17b]"></a>lfs_file_opencfg</STRONG> (Thumb, 552 bytes, Stack size 88 bytes, lfs.o(i.lfs_file_opencfg))
<BR><BR>[Stack]<UL><LI>Max Depth = 1000<LI>Call Chain = lfs_file_opencfg &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_commit &rArr; lfs_dir_compact &rArr; lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_close
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_malloc
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_forceconsistency
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_fromle32
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strlen
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_open
</UL>

<P><STRONG><a name="[13a]"></a>lfs_file_read</STRONG> (Thumb, 262 bytes, Stack size 56 bytes, lfs.o(i.lfs_file_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 232 + In Cycle
<LI>Call Chain = lfs_file_read &rArr;  lfs_file_flush (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
</UL>

<P><STRONG><a name="[13b]"></a>lfs_file_rewind</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, lfs.o(i.lfs_file_rewind))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = lfs_file_rewind &rArr; lfs_file_seek &rArr; lfs_file_flush &rArr; lfs_file_write &rArr;  lfs_file_write (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_seek
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
</UL>

<P><STRONG><a name="[17f]"></a>lfs_file_seek</STRONG> (Thumb, 74 bytes, Stack size 24 bytes, lfs.o(i.lfs_file_seek))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = lfs_file_seek &rArr; lfs_file_flush &rArr; lfs_file_write &rArr;  lfs_file_write (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
</UL>
<BR>[Called By]<UL><LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_rewind
</UL>

<P><STRONG><a name="[179]"></a>lfs_file_sync</STRONG> (Thumb, 202 bytes, Stack size 48 bytes, lfs.o(i.lfs_file_sync))
<BR><BR>[Stack]<UL><LI>Max Depth = 816<LI>Call Chain = lfs_file_sync &rArr; lfs_dir_commit &rArr; lfs_dir_compact &rArr; lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_outline
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>
<BR>[Called By]<UL><LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_close
</UL>

<P><STRONG><a name="[13d]"></a>lfs_file_write</STRONG> (Thumb, 428 bytes, Stack size 64 bytes, lfs.o(i.lfs_file_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 472 + In Cycle
<LI>Call Chain = lfs_file_write &rArr;  lfs_file_write (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_outline
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
</UL>

<P><STRONG><a name="[138]"></a>lfs_format</STRONG> (Thumb, 230 bytes, Stack size 104 bytes, lfs.o(i.lfs_format))
<BR><BR>[Stack]<UL><LI>Max Depth = 872<LI>Call Chain = lfs_format &rArr; lfs_dir_commit &rArr; lfs_dir_compact &rArr; lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_init
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_alloc
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_deinit
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
</UL>

<P><STRONG><a name="[16d]"></a>lfs_fs_size</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs.o(i.lfs_fs_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = lfs_fs_size &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
</UL>

<P><STRONG><a name="[133]"></a>lfs_fs_traverse</STRONG> (Thumb, 256 bytes, Stack size 80 bytes, lfs.o(i.lfs_fs_traverse))
<BR><BR>[Stack]<UL><LI>Max Depth = 304<LI>Call Chain = lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_traverse
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_fromle32
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_size
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
</UL>

<P><STRONG><a name="[137]"></a>lfs_mount</STRONG> (Thumb, 414 bytes, Stack size 112 bytes, lfs.o(i.lfs_mount))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = lfs_mount &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_isdelete
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_init
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmove
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_deinit
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
</UL>

<P><STRONG><a name="[18a]"></a>lfs_storage_init</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, lfs_port.o(i.lfs_storage_init))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[69]"></a>main</STRONG> (Thumb, 134 bytes, Stack size 0 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 1128<LI>Call Chain = main &rArr; lfs_basic_test &rArr; lfs_file_open &rArr; lfs_file_opencfg &rArr; lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_commit &rArr; lfs_dir_compact &rArr; lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_init
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_run
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;scheduler_init
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_storage_init
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;app_btn_init
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowString
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Refresh_GRAM
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART3_UART_Init
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART2_UART_Init
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_USART1_UART_Init
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM6_Init
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_TIM3_Init
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_SPI2_Init
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_I2C1_Init
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_GPIO_Init
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DMA_Init
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_DAC_Init
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MX_ADC1_Init
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Init
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemClock_Config
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[187]"></a>malloc</STRONG> (Thumb, 92 bytes, Stack size 20 bytes, malloc.o(i.malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_malloc
</UL>

<P><STRONG><a name="[136]"></a>my_printf</STRONG> (Thumb, 50 bytes, Stack size 544 bytes, usart_app.o(i.my_printf))
<BR><BR>[Stack]<UL><LI>Max Depth = 608<LI>Call Chain = my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;vsnprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_basic_test
<LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_task
</UL>

<P><STRONG><a name="[75]"></a>prv_btn_event</STRONG> (Thumb, 122 bytes, Stack size 0 bytes, btn_app.o(i.prv_btn_event))
<BR>[Address Reference Count : 1]<UL><LI> btn_app.o(i.app_btn_init)
</UL>
<P><STRONG><a name="[76]"></a>prv_btn_get_state</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, btn_app.o(i.prv_btn_get_state))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = prv_btn_get_state
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_ReadPin
</UL>
<BR>[Address Reference Count : 1]<UL><LI> btn_app.o(i.app_btn_init)
</UL>
<P><STRONG><a name="[188]"></a>scheduler_init</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, scheduler.o(i.scheduler_init))
<BR><BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[18b]"></a>scheduler_run</STRONG> (Thumb, 56 bytes, Stack size 24 bytes, scheduler.o(i.scheduler_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = scheduler_run
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[152]"></a>spi_flash_buffer_read</STRONG> (Thumb, 88 bytes, Stack size 24 bytes, gd25qxx.o(i.spi_flash_buffer_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = spi_flash_buffer_read &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_deskio_read
</UL>

<P><STRONG><a name="[151]"></a>spi_flash_buffer_write</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, gd25qxx.o(i.spi_flash_buffer_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_deskio_prog
</UL>

<P><STRONG><a name="[189]"></a>spi_flash_init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, gd25qxx.o(i.spi_flash_init))
<BR><BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[18e]"></a>spi_flash_page_write</STRONG> (Thumb, 94 bytes, Stack size 24 bytes, gd25qxx.o(i.spi_flash_page_write))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
</UL>
<BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>

<P><STRONG><a name="[150]"></a>spi_flash_sector_erase</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, gd25qxx.o(i.spi_flash_sector_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = spi_flash_sector_erase &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_deskio_erase
</UL>

<P><STRONG><a name="[18d]"></a>spi_flash_send_byte</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, gd25qxx.o(i.spi_flash_send_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_write_enable
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_wait_for_write_end
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>

<P><STRONG><a name="[190]"></a>spi_flash_wait_for_write_end</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, gd25qxx.o(i.spi_flash_wait_for_write_end))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = spi_flash_wait_for_write_end &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>

<P><STRONG><a name="[18f]"></a>spi_flash_write_enable</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, gd25qxx.o(i.spi_flash_write_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = spi_flash_write_enable &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_send_byte
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GPIO_WritePin
</UL>
<BR>[Called By]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_page_write
</UL>

<P><STRONG><a name="[3]"></a>uart_task</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, usart_app.o(i.uart_task))
<BR><BR>[Stack]<UL><LI>Max Depth = 616<LI>Call Chain = uart_task &rArr; my_printf &rArr; HAL_UART_Transmit &rArr; UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_printf
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Address Reference Count : 1]<UL><LI> scheduler.o(.data)
</UL><P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[9d]"></a>ADC_Init</STRONG> (Thumb, 284 bytes, Stack size 8 bytes, stm32f4xx_hal_adc.o(i.ADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_ADC_Init
</UL>

<P><STRONG><a name="[a9]"></a>DMA_CalcBaseAndBitshift</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[a8]"></a>DMA_CheckFifoParam</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_CheckFifoParam
</UL>
<BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Init
</UL>

<P><STRONG><a name="[ab]"></a>DMA_SetConfig</STRONG> (Thumb, 40 bytes, Stack size 12 bytes, stm32f4xx_hal_dma.o(i.DMA_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DMA_SetConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Start_IT
</UL>

<P><STRONG><a name="[cc]"></a>__NVIC_SetPriority</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority))
<BR><BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_NVIC_SetPriority
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SYSTICK_Config
</UL>

<P><STRONG><a name="[6f]"></a>I2C_DMAAbort</STRONG> (Thumb, 182 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.I2C_DMAAbort))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_DMAAbort
</UL>
<BR>[Calls]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_ErrorCallback
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_AbortCpltCallback
</UL>
<BR>[Address Reference Count : 2]<UL><LI> stm32f4xx_hal_i2c.o(i.I2C_ITError)
<LI> stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF)
</UL>
<P><STRONG><a name="[6d]"></a>I2C_DMAError</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.I2C_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_GetError
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_ErrorCallback
</UL>
<BR>[Address Reference Count : 2]<UL><LI> stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA)
<LI> stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA)
</UL>
<P><STRONG><a name="[6c]"></a>I2C_DMAXferCplt</STRONG> (Thumb, 274 bytes, Stack size 24 bytes, stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = I2C_DMAXferCplt
</UL>
<BR>[Calls]<UL><LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_SlaveTxCpltCallback
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_SlaveRxCpltCallback
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MemRxCpltCallback
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MasterRxCpltCallback
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_ErrorCallback
</UL>
<BR>[Address Reference Count : 2]<UL><LI> stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA)
<LI> stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA)
</UL>
<P><STRONG><a name="[f7]"></a>I2C_Flush_DR</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c.o(i.I2C_Flush_DR))
<BR><BR>[Called By]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MemoryTransmit_TXE_BTF
</UL>

<P><STRONG><a name="[f5]"></a>I2C_ITError</STRONG> (Thumb, 336 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.I2C_ITError))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_ITError
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_ListenCpltCallback
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_ErrorCallback
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_AbortCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Slave_STOPF
</UL>

<P><STRONG><a name="[fb]"></a>I2C_IsAcknowledgeFailed</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnBTFFlagUntilTimeout
</UL>

<P><STRONG><a name="[b5]"></a>I2C_MasterReceive_BTF</STRONG> (Thumb, 218 bytes, Stack size 8 bytes, stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C_MasterReceive_BTF
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MemRxCpltCallback
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MasterRxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[b4]"></a>I2C_MasterReceive_RXNE</STRONG> (Thumb, 238 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_MasterReceive_RXNE
</UL>
<BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MemRxCpltCallback
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MasterRxCpltCallback
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_ErrorCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[b3]"></a>I2C_MasterTransmit_BTF</STRONG> (Thumb, 130 bytes, Stack size 8 bytes, stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = I2C_MasterTransmit_BTF &rArr; HAL_I2C_MasterTxCpltCallback &rArr; HAL_I2C_Mem_Write_DMA &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MemTxCpltCallback
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MasterTxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[b1]"></a>I2C_MasterTransmit_TXE</STRONG> (Thumb, 182 bytes, Stack size 8 bytes, stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = I2C_MasterTransmit_TXE &rArr; HAL_I2C_MasterTxCpltCallback &rArr; HAL_I2C_Mem_Write_DMA &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MemoryTransmit_TXE_BTF
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MemTxCpltCallback
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MasterTxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[b0]"></a>I2C_Master_ADDR</STRONG> (Thumb, 276 bytes, Stack size 20 bytes, stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = I2C_Master_ADDR
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[af]"></a>I2C_Master_SB</STRONG> (Thumb, 140 bytes, Stack size 0 bytes, stm32f4xx_hal_i2c.o(i.I2C_Master_SB))
<BR><BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[b2]"></a>I2C_MemoryTransmit_TXE_BTF</STRONG> (Thumb, 168 bytes, Stack size 8 bytes, stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = I2C_MemoryTransmit_TXE_BTF &rArr; HAL_I2C_MemTxCpltCallback &rArr; HAL_I2C_Master_Transmit_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Flush_DR
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_MemTxCpltCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_MasterTransmit_TXE
</UL>

<P><STRONG><a name="[c3]"></a>I2C_RequestMemoryWrite</STRONG> (Thumb, 162 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnTXEFlagUntilTimeout
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnMasterAddressFlagUntilTimeout
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write_DMA
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[ae]"></a>I2C_Slave_ADDR</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_Slave_ADDR
</UL>
<BR>[Calls]<UL><LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_AddrCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[b6]"></a>I2C_Slave_STOPF</STRONG> (Thumb, 338 bytes, Stack size 24 bytes, stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = I2C_Slave_STOPF &rArr; I2C_ITError
</UL>
<BR>[Calls]<UL><LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_GetState
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_DMA_Abort_IT
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_SlaveRxCpltCallback
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_ListenCpltCallback
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_ITError
</UL>
<BR>[Called By]<UL><LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_EV_IRQHandler
</UL>

<P><STRONG><a name="[c5]"></a>I2C_WaitOnBTFFlagUntilTimeout</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnBTFFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>

<P><STRONG><a name="[bd]"></a>I2C_WaitOnFlagUntilTimeout</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_IsDeviceReady
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>

<P><STRONG><a name="[f8]"></a>I2C_WaitOnMasterAddressFlagUntilTimeout</STRONG> (Thumb, 188 bytes, Stack size 32 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>

<P><STRONG><a name="[c4]"></a>I2C_WaitOnTXEFlagUntilTimeout</STRONG> (Thumb, 86 bytes, Stack size 16 bytes, stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = I2C_WaitOnTXEFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_IsAcknowledgeFailed
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
<LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_RequestMemoryWrite
</UL>

<P><STRONG><a name="[d4]"></a>SPI_EndRxTxTransaction</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, stm32f4xx_hal_spi.o(i.SPI_EndRxTxTransaction))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_SPI_TransmitReceive
</UL>

<P><STRONG><a name="[117]"></a>SPI_WaitFlagStateUntilTimeout</STRONG> (Thumb, 180 bytes, Stack size 32 bytes, stm32f4xx_hal_spi.o(i.SPI_WaitFlagStateUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
</UL>
<BR>[Called By]<UL><LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SPI_EndRxTxTransaction
</UL>

<P><STRONG><a name="[dc]"></a>TIM_ITRx_SetConfig</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[da]"></a>TIM_TI1_ConfigInputStage</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_TI1_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[db]"></a>TIM_TI2_ConfigInputStage</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_TI2_ConfigInputStage
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_TIM_ConfigClockSource
</UL>

<P><STRONG><a name="[6e]"></a>UART_DMAAbortOnError</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = UART_DMAAbortOnError
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
</UL>
<P><STRONG><a name="[72]"></a>UART_DMAError</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_DMAError))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_DMAError
</UL>
<BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_ErrorCallback
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[70]"></a>UART_DMAReceiveCplt</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = UART_DMAReceiveCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[71]"></a>UART_DMARxHalfCplt</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = UART_DMARxHalfCplt &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxHalfCpltCallback
</UL>
<BR>[Address Reference Count : 1]<UL><LI> stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
</UL>
<P><STRONG><a name="[e3]"></a>UART_EndRxTransfer</STRONG> (Thumb, 78 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_EndRxTransfer))
<BR><BR>[Calls]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_WaitOnFlagUntilTimeout
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[e2]"></a>UART_EndTxTransfer</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_hal_uart.o(i.UART_EndTxTransfer))
<BR><BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_DMAStop
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndTxTransfer
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_DMAError
</UL>

<P><STRONG><a name="[e5]"></a>UART_Receive_IT</STRONG> (Thumb, 194 bytes, Stack size 8 bytes, stm32f4xx_hal_uart.o(i.UART_Receive_IT))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = UART_Receive_IT &rArr; HAL_UARTEx_RxEventCallback &rArr; HAL_UARTEx_ReceiveToIdle_DMA &rArr; UART_Start_Receive_DMA &rArr; HAL_DMA_Start_IT &rArr; DMA_SetConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_RxCpltCallback
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UARTEx_RxEventCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_IRQHandler
</UL>

<P><STRONG><a name="[ea]"></a>UART_SetConfig</STRONG> (Thumb, 258 bytes, Stack size 16 bytes, stm32f4xx_hal_uart.o(i.UART_SetConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = UART_SetConfig &rArr; __aeabi_uldivmod
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK2Freq
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_RCC_GetPCLK1Freq
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Init
</UL>

<P><STRONG><a name="[ef]"></a>UART_WaitOnFlagUntilTimeout</STRONG> (Thumb, 114 bytes, Stack size 32 bytes, stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = UART_WaitOnFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_GetTick
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_EndRxTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_UART_Transmit
</UL>

<P><STRONG><a name="[141]"></a>lfs_alignup</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, lfs.o(i.lfs_alignup))
<BR><BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
</UL>

<P><STRONG><a name="[131]"></a>lfs_alloc</STRONG> (Thumb, 204 bytes, Stack size 24 bytes, lfs.o(i.lfs_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 328<LI>Call Chain = lfs_alloc &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_alloc
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[77]"></a>lfs_alloc_lookahead</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, lfs.o(i.lfs_alloc_lookahead))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_alloc_lookahead
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_alloc)
</UL>
<P><STRONG><a name="[13e]"></a>lfs_bd_cmp</STRONG> (Thumb, 102 bytes, Stack size 72 bytes, lfs.o(i.lfs_bd_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find_match
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
</UL>

<P><STRONG><a name="[145]"></a>lfs_bd_erase</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lfs.o(i.lfs_bd_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_bd_erase
</UL>
<BR>[Called By]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[140]"></a>lfs_bd_flush</STRONG> (Thumb, 132 bytes, Stack size 40 bytes, lfs.o(i.lfs_bd_flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_cmp
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alignup
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
</UL>

<P><STRONG><a name="[143]"></a>lfs_bd_prog</STRONG> (Thumb, 178 bytes, Stack size 56 bytes, lfs.o(i.lfs_bd_prog))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = lfs_bd_prog &rArr; lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitprog
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[13f]"></a>lfs_bd_read</STRONG> (Thumb, 252 bytes, Stack size 56 bytes, lfs.o(i.lfs_bd_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alignup
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent_match
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_alloc
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_traverse
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_cmp
</UL>

<P><STRONG><a name="[142]"></a>lfs_cache_zero</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lfs.o(i.lfs_cache_zero))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_cache_zero
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_init
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
</UL>

<P><STRONG><a name="[147]"></a>lfs_ctz</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, lfs.o(i.lfs_ctz))
<BR><BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[144]"></a>lfs_ctz_extend</STRONG> (Thumb, 354 bytes, Stack size 80 bytes, lfs.o(i.lfs_ctz_extend))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = lfs_ctz_extend &rArr; lfs_alloc &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_index
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_erase
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
</UL>

<P><STRONG><a name="[14a]"></a>lfs_ctz_find</STRONG> (Thumb, 166 bytes, Stack size 72 bytes, lfs.o(i.lfs_ctz_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = lfs_ctz_find &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_index
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
</UL>

<P><STRONG><a name="[14b]"></a>lfs_ctz_fromle32</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs.o(i.lfs_ctz_fromle32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_ctz_fromle32
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
</UL>

<P><STRONG><a name="[146]"></a>lfs_ctz_index</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, lfs.o(i.lfs_ctz_index))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = lfs_ctz_index
</UL>
<BR>[Calls]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_popc
</UL>
<BR>[Called By]<UL><LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_traverse
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[14d]"></a>lfs_ctz_traverse</STRONG> (Thumb, 156 bytes, Stack size 80 bytes, lfs.o(i.lfs_ctz_traverse))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = lfs_ctz_traverse &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_index
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
</UL>

<P><STRONG><a name="[14e]"></a>lfs_deinit</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, lfs.o(i.lfs_deinit))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_deinit &rArr; free
</UL>
<BR>[Calls]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;free
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_init
</UL>

<P><STRONG><a name="[153]"></a>lfs_dir_alloc</STRONG> (Thumb, 128 bytes, Stack size 40 bytes, lfs.o(i.lfs_dir_alloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = lfs_dir_alloc &rArr; lfs_alloc &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_split
</UL>

<P><STRONG><a name="[154]"></a>lfs_dir_commit</STRONG> (Thumb, 782 bytes, Stack size 168 bytes, lfs.o(i.lfs_dir_commit))
<BR><BR>[Stack]<UL><LI>Max Depth = 768<LI>Call Chain = lfs_dir_commit &rArr; lfs_dir_compact &rArr; lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_xormove
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_tole32
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_iszero
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_fromle32
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_outline
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_sync
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_demove
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
</UL>

<P><STRONG><a name="[78]"></a>lfs_dir_commit_commit</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, lfs.o(i.lfs_dir_commit_commit))
<BR><BR>[Stack]<UL><LI>Max Depth = 320<LI>Call Chain = lfs_dir_commit_commit &rArr; lfs_dir_commitattr &rArr; lfs_dir_commitprog &rArr; lfs_bd_prog &rArr; lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
</UL>
<BR>[Address Reference Count : 2]<UL><LI> lfs.o(i.lfs_dir_commit)
<LI> lfs.o(i.lfs_dir_compact)
</UL>
<P><STRONG><a name="[79]"></a>lfs_dir_commit_size</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, lfs.o(i.lfs_dir_commit_size))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_dir_commit_size &rArr; lfs_tag_dsize
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_dir_compact)
</UL>
<P><STRONG><a name="[163]"></a>lfs_dir_commitattr</STRONG> (Thumb, 170 bytes, Stack size 56 bytes, lfs.o(i.lfs_dir_commitattr))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = lfs_dir_commitattr &rArr; lfs_dir_commitprog &rArr; lfs_bd_prog &rArr; lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_frombe32
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitprog
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit_commit
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[165]"></a>lfs_dir_commitcrc</STRONG> (Thumb, 388 bytes, Stack size 64 bytes, lfs.o(i.lfs_dir_commitcrc))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = lfs_dir_commitcrc &rArr; lfs_bd_prog &rArr; lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_crc
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_frombe32
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alignup
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[16a]"></a>lfs_dir_commitprog</STRONG> (Thumb, 58 bytes, Stack size 32 bytes, lfs.o(i.lfs_dir_commitprog))
<BR><BR>[Stack]<UL><LI>Max Depth = 256<LI>Call Chain = lfs_dir_commitprog &rArr; lfs_bd_prog &rArr; lfs_bd_flush &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_crc
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
</UL>

<P><STRONG><a name="[166]"></a>lfs_dir_compact</STRONG> (Thumb, 818 bytes, Stack size 152 bytes, lfs.o(i.lfs_dir_compact))
<BR><BR>[Stack]<UL><LI>Max Depth = 600<LI>Call Chain = lfs_dir_compact &rArr; lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_size
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_xormove
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_tole32
<LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_iszero
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_fromle32
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_split
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitprog
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_erase
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alignup
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_split
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[15c]"></a>lfs_dir_drop</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, lfs.o(i.lfs_dir_drop))
<BR><BR>[Stack]<UL><LI>Max Depth = 200 + In Cycle
<LI>Call Chain = lfs_dir_drop &rArr;  lfs_dir_commit (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>
<BR>[Called By]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[167]"></a>lfs_dir_fetch</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, lfs.o(i.lfs_dir_fetch))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_demove
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[170]"></a>lfs_dir_fetchmatch</STRONG> (Thumb, 936 bytes, Stack size 144 bytes, lfs.o(i.lfs_dir_fetchmatch))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_crc
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_frombe32
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
</UL>

<P><STRONG><a name="[171]"></a>lfs_dir_find</STRONG> (Thumb, 318 bytes, Stack size 88 bytes, lfs.o(i.lfs_dir_find))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = lfs_dir_find &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcspn
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strspn
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strchr
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
</UL>

<P><STRONG><a name="[7a]"></a>lfs_dir_find_match</STRONG> (Thumb, 82 bytes, Stack size 40 bytes, lfs.o(i.lfs_dir_find_match))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = lfs_dir_find_match &rArr; lfs_bd_cmp &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_cmp
</UL>
<BR>[Address Reference Count : 2]<UL><LI> lfs.o(i.lfs_dir_find)
<LI> lfs.o(i.lfs_mount)
</UL>
<P><STRONG><a name="[174]"></a>lfs_dir_get</STRONG> (Thumb, 20 bytes, Stack size 32 bytes, lfs.o(i.lfs_dir_get))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = lfs_dir_get &rArr; lfs_dir_getslice &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
</UL>

<P><STRONG><a name="[161]"></a>lfs_dir_getgstate</STRONG> (Thumb, 70 bytes, Stack size 24 bytes, lfs.o(i.lfs_dir_getgstate))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = lfs_dir_getgstate &rArr; lfs_dir_get &rArr; lfs_dir_getslice &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_fromle32
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[177]"></a>lfs_dir_getread</STRONG> (Thumb, 230 bytes, Stack size 64 bytes, lfs.o(i.lfs_dir_getread))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = lfs_dir_getread &rArr; lfs_dir_getslice &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alignup
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
</UL>

<P><STRONG><a name="[176]"></a>lfs_dir_getslice</STRONG> (Thumb, 280 bytes, Stack size 56 bytes, lfs.o(i.lfs_dir_getslice))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = lfs_dir_getslice &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_isdelete
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_min
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_frombe32
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
</UL>

<P><STRONG><a name="[16c]"></a>lfs_dir_split</STRONG> (Thumb, 112 bytes, Stack size 80 bytes, lfs.o(i.lfs_dir_split))
<BR><BR>[Stack]<UL><LI>Max Depth = 448 + In Cycle
<LI>Call Chain = lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_alloc
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
</UL>

<P><STRONG><a name="[15f]"></a>lfs_dir_traverse</STRONG> (Thumb, 436 bytes, Stack size 104 bytes, lfs.o(i.lfs_dir_traverse))
<BR><BR>[Stack]<UL><LI>Max Depth = 160 + In Cycle
<LI>Call Chain = lfs_dir_traverse &rArr;  lfs_dir_traverse (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
<LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_frombe32
<LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[7b]"></a>lfs_dir_traverse_filter</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, lfs.o(i.lfs_dir_traverse_filter))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_dir_traverse_filter
</UL>
<BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_isdelete
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_dir_traverse)
</UL>
<P><STRONG><a name="[157]"></a>lfs_file_flush</STRONG> (Thumb, 232 bytes, Stack size 112 bytes, lfs.o(i.lfs_file_flush))
<BR><BR>[Stack]<UL><LI>Max Depth = 584<LI>Call Chain = lfs_file_flush &rArr; lfs_file_write &rArr;  lfs_file_write (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_flush
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_read
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_sync
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_seek
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[156]"></a>lfs_file_outline</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, lfs.o(i.lfs_file_outline))
<BR><BR>[Stack]<UL><LI>Max Depth = 400<LI>Call Chain = lfs_file_outline &rArr; lfs_file_relocate &rArr; lfs_alloc &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_relocate
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_sync
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[17a]"></a>lfs_file_relocate</STRONG> (Thumb, 232 bytes, Stack size 64 bytes, lfs.o(i.lfs_file_relocate))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = lfs_file_relocate &rArr; lfs_alloc &rArr; lfs_fs_traverse &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_prog
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_erase
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_outline
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_flush
</UL>

<P><STRONG><a name="[169]"></a>lfs_frombe32</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lfs.o(i.lfs_frombe32))
<BR><BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
</UL>

<P><STRONG><a name="[149]"></a>lfs_fromle32</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lfs.o(i.lfs_fromle32))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_fromle32
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_alloc
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_traverse
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_fromle32
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[181]"></a>lfs_fs_demove</STRONG> (Thumb, 70 bytes, Stack size 48 bytes, lfs.o(i.lfs_fs_demove))
<BR><BR>[Stack]<UL><LI>Max Depth = 816<LI>Call Chain = lfs_fs_demove &rArr; lfs_dir_commit &rArr; lfs_dir_compact &rArr; lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmove
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_forceconsistency
</UL>

<P><STRONG><a name="[183]"></a>lfs_fs_deorphan</STRONG> (Thumb, 284 bytes, Stack size 136 bytes, lfs.o(i.lfs_fs_deorphan))
<BR><BR>[Stack]<UL><LI>Max Depth = 904<LI>Call Chain = lfs_fs_deorphan &rArr; lfs_dir_commit &rArr; lfs_dir_compact &rArr; lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_preporphans
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_get
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_forceconsistency
</UL>

<P><STRONG><a name="[17c]"></a>lfs_fs_forceconsistency</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, lfs.o(i.lfs_fs_forceconsistency))
<BR><BR>[Stack]<UL><LI>Max Depth = 912<LI>Call Chain = lfs_fs_forceconsistency &rArr; lfs_fs_deorphan &rArr; lfs_dir_commit &rArr; lfs_dir_compact &rArr; lfs_dir_split &rArr;  lfs_dir_compact (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_demove
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
</UL>

<P><STRONG><a name="[184]"></a>lfs_fs_parent</STRONG> (Thumb, 96 bytes, Stack size 64 bytes, lfs.o(i.lfs_fs_parent))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = lfs_fs_parent &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
</UL>

<P><STRONG><a name="[7c]"></a>lfs_fs_parent_match</STRONG> (Thumb, 62 bytes, Stack size 40 bytes, lfs.o(i.lfs_fs_parent_match))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = lfs_fs_parent_match &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_fs_parent)
</UL>
<P><STRONG><a name="[15b]"></a>lfs_fs_pred</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, lfs.o(i.lfs_fs_pred))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = lfs_fs_pred &rArr; lfs_dir_fetch &rArr; lfs_dir_fetchmatch &rArr; lfs_bd_read
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_isnull
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetch
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[185]"></a>lfs_fs_preporphans</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, lfs.o(i.lfs_fs_preporphans))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_fs_preporphans
</UL>
<BR>[Calls]<UL><LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasorphans
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
</UL>

<P><STRONG><a name="[16f]"></a>lfs_fs_relocate</STRONG> (Thumb, 222 bytes, Stack size 64 bytes, lfs.o(i.lfs_fs_relocate))
<BR><BR>[Stack]<UL><LI>Max Depth = 328 + In Cycle
<LI>Call Chain = lfs_fs_relocate &rArr;  lfs_dir_commit (Cycle)
</UL>
<BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_fromle32
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_preporphans
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
</UL>

<P><STRONG><a name="[7d]"></a>lfs_fs_size_count</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lfs.o(i.lfs_fs_size_count))
<BR>[Address Reference Count : 1]<UL><LI> lfs.o(i.lfs_fs_size)
</UL>
<P><STRONG><a name="[164]"></a>lfs_gstate_fromle32</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, lfs.o(i.lfs_gstate_fromle32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_gstate_fromle32
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
</UL>
<BR>[Called By]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getgstate
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[182]"></a>lfs_gstate_hasmove</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, lfs.o(i.lfs_gstate_hasmove))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_demove
</UL>

<P><STRONG><a name="[159]"></a>lfs_gstate_hasmovehere</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, lfs.o(i.lfs_gstate_hasmovehere))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = lfs_gstate_hasmovehere &rArr; lfs_pair_cmp
</UL>
<BR>[Calls]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_cmp
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[186]"></a>lfs_gstate_hasorphans</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, lfs.o(i.lfs_gstate_hasorphans))
<BR><BR>[Called By]<UL><LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_preporphans
</UL>

<P><STRONG><a name="[160]"></a>lfs_gstate_iszero</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, lfs.o(i.lfs_gstate_iszero))
<BR><BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[162]"></a>lfs_gstate_tole32</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, lfs.o(i.lfs_gstate_tole32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_gstate_tole32
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[15a]"></a>lfs_gstate_xormove</STRONG> (Thumb, 92 bytes, Stack size 12 bytes, lfs.o(i.lfs_gstate_xormove))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = lfs_gstate_xormove
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[180]"></a>lfs_init</STRONG> (Thumb, 284 bytes, Stack size 16 bytes, lfs.o(i.lfs_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = lfs_init &rArr; lfs_malloc &rArr; malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_malloc
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_deinit
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_cache_zero
</UL>
<BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
</UL>

<P><STRONG><a name="[17e]"></a>lfs_malloc</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lfs.o(i.lfs_malloc))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = lfs_malloc &rArr; malloc
</UL>
<BR>[Calls]<UL><LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;malloc
</UL>
<BR>[Called By]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_init
</UL>

<P><STRONG><a name="[132]"></a>lfs_min</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, lfs.o(i.lfs_min))
<BR><BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_write
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_opencfg
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getread
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find_match
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_find
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_bd_read
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_alloc
</UL>

<P><STRONG><a name="[155]"></a>lfs_pair_cmp</STRONG> (Thumb, 34 bytes, Stack size 8 bytes, lfs.o(i.lfs_pair_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_pair_cmp
</UL>
<BR>[Called By]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_hasmovehere
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent_match
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_split
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[158]"></a>lfs_pair_fromle32</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs.o(i.lfs_pair_fromle32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_pair_fromle32
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent_match
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_find
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[16e]"></a>lfs_pair_isnull</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, lfs.o(i.lfs_pair_isnull))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_traverse
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_sync
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_pred
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_parent
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
</UL>

<P><STRONG><a name="[15e]"></a>lfs_pair_tole32</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs.o(i.lfs_pair_tole32))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_pair_tole32
</UL>
<BR>[Calls]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tole32
</UL>
<BR>[Called By]<UL><LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_relocate
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fs_deorphan
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_drop
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit
</UL>

<P><STRONG><a name="[14c]"></a>lfs_popc</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, lfs.o(i.lfs_popc))
<BR><BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_index
</UL>

<P><STRONG><a name="[168]"></a>lfs_tag_dsize</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, lfs.o(i.lfs_tag_dsize))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = lfs_tag_dsize
</UL>
<BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_isdelete
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitattr
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commit_size
</UL>

<P><STRONG><a name="[178]"></a>lfs_tag_isdelete</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, lfs.o(i.lfs_tag_isdelete))
<BR><BR>[Called By]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_mount
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_tag_dsize
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_traverse_filter
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_getslice
</UL>

<P><STRONG><a name="[148]"></a>lfs_tole32</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lfs.o(i.lfs_tole32))
<BR><BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_fromle32
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_format
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_file_sync
<LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_pair_tole32
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_gstate_tole32
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_fetchmatch
<LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_compact
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_dir_commitcrc
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;lfs_ctz_extend
</UL>

<P><STRONG><a name="[80]"></a>lfs_deskio_erase</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, lfs_port.o(i.lfs_deskio_erase))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = lfs_deskio_erase &rArr; spi_flash_sector_erase &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_sector_erase
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs_port.o(i.lfs_storage_init)
</UL>
<P><STRONG><a name="[7f]"></a>lfs_deskio_prog</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs_port.o(i.lfs_deskio_prog))
<BR><BR>[Stack]<UL><LI>Max Depth = 208<LI>Call Chain = lfs_deskio_prog &rArr; spi_flash_buffer_write &rArr; spi_flash_page_write &rArr; spi_flash_write_enable &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_write
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs_port.o(i.lfs_storage_init)
</UL>
<P><STRONG><a name="[7e]"></a>lfs_deskio_read</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, lfs_port.o(i.lfs_deskio_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = lfs_deskio_read &rArr; spi_flash_buffer_read &rArr; spi_flash_send_byte &rArr; HAL_SPI_TransmitReceive &rArr; SPI_EndRxTxTransaction &rArr; SPI_WaitFlagStateUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_flash_buffer_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> lfs_port.o(i.lfs_storage_init)
</UL>
<P><STRONG><a name="[81]"></a>lfs_deskio_sync</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, lfs_port.o(i.lfs_deskio_sync))
<BR>[Address Reference Count : 1]<UL><LI> lfs_port.o(i.lfs_storage_init)
</UL>
<P><STRONG><a name="[12f]"></a>bit_array_and</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, ebtn.o(i.bit_array_and))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = bit_array_and
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
</UL>

<P><STRONG><a name="[129]"></a>bit_array_assign</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, ebtn.o(i.bit_array_assign))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = bit_array_assign
</UL>
<BR>[Called By]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process
</UL>

<P><STRONG><a name="[126]"></a>bit_array_cmp</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ebtn.o(i.bit_array_cmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = bit_array_cmp &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
</UL>

<P><STRONG><a name="[12c]"></a>bit_array_get</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, ebtn.o(i.bit_array_get))
<BR><BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn
</UL>

<P><STRONG><a name="[12b]"></a>ebtn_process_btn</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, ebtn.o(i.ebtn_process_btn))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = ebtn_process_btn &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_process_btn
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_get
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>

<P><STRONG><a name="[12e]"></a>ebtn_process_btn_combo</STRONG> (Thumb, 172 bytes, Stack size 40 bytes, ebtn.o(i.ebtn_process_btn_combo))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = ebtn_process_btn_combo &rArr; prv_process_btn
</UL>
<BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;prv_process_btn
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_cmp
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bit_array_and
</UL>
<BR>[Called By]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_with_curr_state
</UL>

<P><STRONG><a name="[12d]"></a>prv_process_btn</STRONG> (Thumb, 328 bytes, Stack size 24 bytes, ebtn.o(i.prv_process_btn))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = prv_process_btn
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn_combo
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ebtn_process_btn
</UL>

<P><STRONG><a name="[112]"></a>OLED_Write_Cmd</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, oled.o(i.OLED_Write_Cmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = OLED_Write_Cmd &rArr; HAL_I2C_Mem_Write &rArr; I2C_RequestMemoryWrite &rArr; I2C_WaitOnMasterAddressFlagUntilTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_Delay
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HAL_I2C_Mem_Write
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[11f]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[11d]"></a>_printf_core</STRONG> (Thumb, 1744 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0vsnprintf
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf
</UL>

<P><STRONG><a name="[122]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[121]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[74]"></a>_snputc</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, printfa.o(i._snputc))
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0vsnprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
