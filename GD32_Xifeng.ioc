#MicroXplorer Configuration settings - do not modify
ADC1.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_10
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_5
ADC1.DMAContinuousRequests=DISABLE
ADC1.ExternalTrigConv=ADC_EXTERNALTRIGCONV_T3_TRGO
ADC1.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,NbrOfConversionFlag,master,Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,NbrOfConversion,ExternalTrigConv,DMAContinuousRequests
ADC1.NbrOfConversion=2
ADC1.NbrOfConversionFlag=1
ADC1.Rank-0\#ChannelRegularConversion=1
ADC1.Rank-1\#ChannelRegularConversion=2
ADC1.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
DAC.DAC_Trigger=DAC_TRIGGER_T6_TRGO
DAC.IPParameters=DAC_Trigger
Dma.ADC1.1.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.1.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.1.Instance=DMA2_Stream0
Dma.ADC1.1.MemDataAlignment=DMA_MDATAALIGN_WORD
Dma.ADC1.1.MemInc=DMA_MINC_ENABLE
Dma.ADC1.1.Mode=DMA_NORMAL
Dma.ADC1.1.PeriphDataAlignment=DMA_PDATAALIGN_WORD
Dma.ADC1.1.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.1.Priority=DMA_PRIORITY_LOW
Dma.ADC1.1.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.I2C1_TX.4.Direction=DMA_MEMORY_TO_PERIPH
Dma.I2C1_TX.4.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.I2C1_TX.4.Instance=DMA1_Stream6
Dma.I2C1_TX.4.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.I2C1_TX.4.MemInc=DMA_MINC_ENABLE
Dma.I2C1_TX.4.Mode=DMA_NORMAL
Dma.I2C1_TX.4.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.I2C1_TX.4.PeriphInc=DMA_PINC_DISABLE
Dma.I2C1_TX.4.Priority=DMA_PRIORITY_LOW
Dma.I2C1_TX.4.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=USART1_RX
Dma.Request1=ADC1
Dma.Request2=USART3_RX
Dma.Request3=USART2_RX
Dma.Request4=I2C1_TX
Dma.RequestsNb=5
Dma.USART1_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART1_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART1_RX.0.Instance=DMA2_Stream2
Dma.USART1_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART1_RX.0.MemInc=DMA_MINC_ENABLE
Dma.USART1_RX.0.Mode=DMA_NORMAL
Dma.USART1_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART1_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.USART1_RX.0.Priority=DMA_PRIORITY_LOW
Dma.USART1_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART2_RX.3.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART2_RX.3.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART2_RX.3.Instance=DMA1_Stream5
Dma.USART2_RX.3.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART2_RX.3.MemInc=DMA_MINC_ENABLE
Dma.USART2_RX.3.Mode=DMA_NORMAL
Dma.USART2_RX.3.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART2_RX.3.PeriphInc=DMA_PINC_DISABLE
Dma.USART2_RX.3.Priority=DMA_PRIORITY_LOW
Dma.USART2_RX.3.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.USART3_RX.2.Direction=DMA_PERIPH_TO_MEMORY
Dma.USART3_RX.2.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.USART3_RX.2.Instance=DMA1_Stream1
Dma.USART3_RX.2.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.USART3_RX.2.MemInc=DMA_MINC_ENABLE
Dma.USART3_RX.2.Mode=DMA_NORMAL
Dma.USART3_RX.2.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.USART3_RX.2.PeriphInc=DMA_PINC_DISABLE
Dma.USART3_RX.2.Priority=DMA_PRIORITY_LOW
Dma.USART3_RX.2.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
File.Version=6
GPIO.groupedBy=Group By Peripherals
I2C1.I2C_Mode=I2C_Standard
I2C1.IPParameters=I2C_Mode
KeepUserPlacement=false
Mcu.CPN=STM32F429ZGT6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=DAC
Mcu.IP10=USART1
Mcu.IP11=USART2
Mcu.IP12=USART3
Mcu.IP2=DMA
Mcu.IP3=I2C1
Mcu.IP4=NVIC
Mcu.IP5=RCC
Mcu.IP6=SPI2
Mcu.IP7=SYS
Mcu.IP8=TIM3
Mcu.IP9=TIM6
Mcu.IPNb=13
Mcu.Name=STM32F429Z(E-G)Tx
Mcu.Package=LQFP144
Mcu.Pin0=PE2
Mcu.Pin1=PC14/OSC32_IN
Mcu.Pin10=PB0
Mcu.Pin11=PE7
Mcu.Pin12=PE9
Mcu.Pin13=PE11
Mcu.Pin14=PE13
Mcu.Pin15=PE15
Mcu.Pin16=PB10
Mcu.Pin17=PB11
Mcu.Pin18=PB12
Mcu.Pin19=PB13
Mcu.Pin2=PC15/OSC32_OUT
Mcu.Pin20=PB14
Mcu.Pin21=PB15
Mcu.Pin22=PD8
Mcu.Pin23=PD9
Mcu.Pin24=PD10
Mcu.Pin25=PD11
Mcu.Pin26=PD12
Mcu.Pin27=PD13
Mcu.Pin28=PA9
Mcu.Pin29=PA10
Mcu.Pin3=PH0/OSC_IN
Mcu.Pin30=PA13
Mcu.Pin31=PA14
Mcu.Pin32=PB8
Mcu.Pin33=PB9
Mcu.Pin34=VP_SYS_VS_Systick
Mcu.Pin35=VP_TIM3_VS_ClockSourceINT
Mcu.Pin36=VP_TIM6_VS_ClockSourceINT
Mcu.Pin4=PH1/OSC_OUT
Mcu.Pin5=PC0
Mcu.Pin6=PA2
Mcu.Pin7=PA3
Mcu.Pin8=PA4
Mcu.Pin9=PA5
Mcu.PinsNb=37
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F429ZGTx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.ADC_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream5_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA1_Stream6_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DMA2_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.I2C1_EV_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:0\:0\:true\:false\:true\:false\:true\:false
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA10.Locked=true
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA2.Locked=true
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Locked=true
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA4.Locked=true
PA4.Signal=COMP_DAC1_group
PA5.Locked=true
PA5.Signal=ADCx_IN5
PA9.Locked=true
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB0.Locked=true
PB0.Signal=GPIO_Input
PB10.Locked=true
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB11.Locked=true
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.Locked=true
PB13.Mode=Full_Duplex_Master
PB13.Signal=SPI2_SCK
PB14.Locked=true
PB14.Mode=Full_Duplex_Master
PB14.Signal=SPI2_MISO
PB15.Locked=true
PB15.Mode=Full_Duplex_Master
PB15.Signal=SPI2_MOSI
PB8.Locked=true
PB8.Mode=I2C
PB8.Signal=I2C1_SCL
PB9.Locked=true
PB9.Mode=I2C
PB9.Signal=I2C1_SDA
PC0.Locked=true
PC0.Signal=ADCx_IN10
PC14/OSC32_IN.Mode=LSE-External-Oscillator
PC14/OSC32_IN.Signal=RCC_OSC32_IN
PC15/OSC32_OUT.Mode=LSE-External-Oscillator
PC15/OSC32_OUT.Signal=RCC_OSC32_OUT
PD10.GPIOParameters=PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD10.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD10.GPIO_PuPd=GPIO_PULLUP
PD10.Locked=true
PD10.PinState=GPIO_PIN_SET
PD10.Signal=GPIO_Output
PD11.GPIOParameters=PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD11.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD11.GPIO_PuPd=GPIO_PULLUP
PD11.Locked=true
PD11.PinState=GPIO_PIN_SET
PD11.Signal=GPIO_Output
PD12.GPIOParameters=PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD12.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD12.GPIO_PuPd=GPIO_PULLUP
PD12.Locked=true
PD12.PinState=GPIO_PIN_SET
PD12.Signal=GPIO_Output
PD13.GPIOParameters=PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD13.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD13.GPIO_PuPd=GPIO_PULLUP
PD13.Locked=true
PD13.PinState=GPIO_PIN_SET
PD13.Signal=GPIO_Output
PD8.GPIOParameters=PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD8.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD8.GPIO_PuPd=GPIO_PULLUP
PD8.Locked=true
PD8.PinState=GPIO_PIN_SET
PD8.Signal=GPIO_Output
PD9.GPIOParameters=PinState,GPIO_PuPd,GPIO_ModeDefaultOutputPP
PD9.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_PP
PD9.GPIO_PuPd=GPIO_PULLUP
PD9.Locked=true
PD9.PinState=GPIO_PIN_SET
PD9.Signal=GPIO_Output
PE11.Locked=true
PE11.Signal=GPIO_Input
PE13.Locked=true
PE13.Signal=GPIO_Input
PE15.Locked=true
PE15.Signal=GPIO_Input
PE2.Locked=true
PE2.Signal=SPI4_SCK
PE7.Locked=true
PE7.Signal=GPIO_Input
PE9.Locked=true
PE9.Signal=GPIO_Input
PH0/OSC_IN.Mode=HSE-External-Oscillator
PH0/OSC_IN.Signal=RCC_OSC_IN
PH1/OSC_OUT.Mode=HSE-External-Oscillator
PH1/OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F429ZGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.1
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=GD32_Xifeng.ioc
ProjectManager.ProjectName=GD32_Xifeng
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_ADC1_Init-ADC1-false-HAL-true,6-MX_DAC_Init-DAC-false-HAL-true,7-MX_TIM3_Init-TIM3-false-HAL-true,8-MX_TIM6_Init-TIM6-false-HAL-true,9-MX_SPI2_Init-SPI2-false-HAL-true,10-MX_USART2_UART_Init-USART2-false-HAL-true,11-MX_USART3_UART_Init-USART3-false-HAL-true,12-MX_I2C1_Init-I2C1-false-HAL-true
RCC.48MHZClocksFreq_Value=90000000
RCC.AHBFreq_Value=180000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=45000000
RCC.APB1TimFreq_Value=90000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=90000000
RCC.APB2TimFreq_Value=180000000
RCC.CortexFreq_Value=180000000
RCC.EthernetFreq_Value=180000000
RCC.FCLKCortexFreq_Value=180000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=180000000
RCC.HSE_VALUE=25000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=160000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LCDTFTFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SAI_AClocksFreq_Value,SAI_BClocksFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOSAIOutputFreq_Value,VCOSAIOutputFreq_ValueQ,VCOSAIOutputFreq_ValueR,VcooutputI2S,VcooutputI2SQ
RCC.LCDTFTFreq_Value=20416666.666666668
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=180000000
RCC.PLLCLKFreq_Value=180000000
RCC.PLLM=15
RCC.PLLN=216
RCC.PLLQCLKFreq_Value=90000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=12500000
RCC.SAI_AClocksFreq_Value=20416666.666666668
RCC.SAI_BClocksFreq_Value=20416666.666666668
RCC.SYSCLKFreq_VALUE=180000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=320000000
RCC.VCOInputFreq_Value=1666666.6666666667
RCC.VCOOutputFreq_Value=360000000
RCC.VCOSAIOutputFreq_Value=81666666.66666667
RCC.VCOSAIOutputFreq_ValueQ=20416666.666666668
RCC.VCOSAIOutputFreq_ValueR=40833333.333333336
RCC.VcooutputI2S=160000000
RCC.VcooutputI2SQ=160000000
SH.ADCx_IN10.0=ADC1_IN10,IN10
SH.ADCx_IN10.ConfNb=1
SH.ADCx_IN5.0=ADC1_IN5,IN5
SH.ADCx_IN5.ConfNb=1
SH.COMP_DAC1_group.0=DAC_OUT1,DAC_OUT1
SH.COMP_DAC1_group.ConfNb=1
SPI2.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_8
SPI2.CLKPhase=SPI_PHASE_2EDGE
SPI2.CLKPolarity=SPI_POLARITY_HIGH
SPI2.CalculateBaudRate=5.625 MBits/s
SPI2.Direction=SPI_DIRECTION_2LINES
SPI2.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,BaudRatePrescaler,CLKPhase,CLKPolarity
SPI2.Mode=SPI_MODE_MASTER
SPI2.VirtualType=VM_MASTER
TIM3.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM3.IPParameters=Period,TIM_MasterOutputTrigger,AutoReloadPreload,Prescaler
TIM3.Period=100-1
TIM3.Prescaler=180-1
TIM3.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
TIM6.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM6.IPParameters=Period,TIM_MasterOutputTrigger,Prescaler,AutoReloadPreload
TIM6.Period=100-1
TIM6.Prescaler=180-1
TIM6.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
USART1.BaudRate=460800
USART1.IPParameters=VirtualMode,BaudRate
USART1.VirtualMode=VM_ASYNC
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM3_VS_ClockSourceINT.Mode=Internal
VP_TIM3_VS_ClockSourceINT.Signal=TIM3_VS_ClockSourceINT
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
board=custom
