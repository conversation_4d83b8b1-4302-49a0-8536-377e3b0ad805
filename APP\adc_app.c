#include "adc_app.h"
#include "adc.h"
#include "math.h"
#include "dac.h"
#include "tim.h"
#include "usart_app.h"
#include "string.h"
#include "usart.h"

// 1 ???
// 2 DMA???????
// 3 DMA TIM ????????
#define ADC_MODE (3)

// --- ?????? --- 
#define SINE_SAMPLES 100    // ?????????????????
#define DAC_MAX_VALUE 4095 // 12 �� DAC ?????????? (2^12 - 1)

uint16_t SineWave[SINE_SAMPLES]; // ?��??????????????

// --- ????????????????? ---
/**
 * @brief ??????????????
 * @param buffer: ?��?????????????????
 * @param samples: ?????????????????
 * @param amplitude: ????????????? (??????????)
 * @param phase_shift: ??��??? (????)
 * @retval None
 */
void Generate_Sine_Wave(uint16_t* buffer, uint32_t samples, uint16_t amplitude, float phase_shift)
{
  // ?????????????????????? (2*PI / samples)
  float step = 2.0f * 3.14159f / samples; 
  
  for(uint32_t i = 0; i < samples; i++)
  {
    // ????????????? (-1.0 ?? 1.0)
    float sine_value = sinf(i * step + phase_shift); // ??? sinf ???��??

    // ?????????? DAC ???????�� (0 - 4095)
    // 1. ?? (-1.0 ~ 1.0) ??? (-amplitude ~ +amplitude)
    // 2. ????????? (DAC_MAX_VALUE / 2)??????��???? (Center-amp ~ Center+amp)
    buffer[i] = (uint16_t)((sine_value * amplitude) + (DAC_MAX_VALUE / 2.0f));
    
    // ????????��??��?? (?��)
    if (buffer[i] > DAC_MAX_VALUE) buffer[i] = DAC_MAX_VALUE;
    // ????????????????????????????????????????????
    // else if (buffer[i] < 0) buffer[i] = 0; 
  }
}

// --- ????????? (?? main ???????????????????) ---
void dac_sin_init(void)
{
    // 1. ??????????????????
    //     amplitude = DAC_MAX_VALUE / 2 ?????????????? (0-4095)
    Generate_Sine_Wave(SineWave, SINE_SAMPLES, DAC_MAX_VALUE / 2, 0.0f);
    
    // 2. ???????? DAC ?????? (???? TIM6)
    HAL_TIM_Base_Start(&htim6); // htim6 ?? TIM6 ????
    
    // 3. ???? DAC ???????? DMA ????????????
    //    hdac: DAC ???
    //    DAC_CHANNEL_1: ????? DAC ???
    //    (uint32_t *)SineWave: ??????????? (HAL ???? uint32_t*)
    //    SINE_SAMPLES: ??????��???? (DMA ???????)
    //    DAC_ALIGN_12B_R: ???????? (12 ��?????)
    HAL_DAC_Start_DMA(&hdac, DAC_CHANNEL_1, (uint32_t *)SineWave, SINE_SAMPLES, DAC_ALIGN_12B_R);
}

// --- ?????????????? --- 
// ??? dac_sin_init ??????????????????????????
// adc_task() ?��?????? dac ???????

#if ADC_MODE == 1

__IO uint32_t adc_val;  // ????��????????? ADC ?
__IO float voltage; // ????��?????????

// ???????? ADC ????????????????????????
void adc_read_by_polling(void) 
{
    // 1. ???? ADC ???
    HAL_ADC_Start(&hadc1); // hadc1 ????? ADC ???

    // 2. ????????? (?????)
    //    ???? 1000 ????????? (????)
    if (HAL_ADC_PollForConversion(&hadc1, 1000) == HAL_OK) 
    {
        // 3. ????????????????? (0-4095 for 12-bit)
        adc_val = HAL_ADC_GetValue(&hadc1);

        // 4. (???) ?????????????????
        //    ???? Vref = 3.3V, ????? 12 �� (4096)
        voltage = (float)adc_val * 3.3f / 4096.0f; 

        // (????????????? voltage ?? adc_val ????????)
        // my_printf(&huart1, "ADC Value: %lu, Voltage: %.2fV\n", adc_val, voltage);

    } 
    else 
    {
        // ???????????????
        // my_printf(&huart1, "ADC Poll Timeout!\n");
    }
    
    // 5. ?????????? ADC ???????????????????????????????
    //    ??????????????????????? HAL_ADC_Stop(&hadc1);
    // HAL_ADC_Stop(&hadc1); // ??????? CubeMX ?????????????
}

#elif ADC_MODE == 2

// --- ?????? --- 
#define ADC_DMA_BUFFER_SIZE 32 // DMA????????��????????????????
uint16_t adc_dma_buffer[ADC_DMA_BUFFER_SIZE]; // DMA ???????
__IO uint32_t adc_val;  // ????��????????? ADC ?
__IO float voltage; // ????��?????????

// --- ????? (????? main ???????????????????��??????) ---
void adc_dma_init(void)
{
    // ???? ADC ????? DMA ????
    // hadc1: ADC ???
    // (uint32_t*)adc_dma_buffer: DMA ?????????? (HAL????????uint32_t*)
    // ADC_DMA_BUFFER_SIZE: ???��?????????? (????????��)
    HAL_ADC_Start_DMA(&hadc1, (uint32_t*)adc_dma_buffer, ADC_DMA_BUFFER_SIZE);
}

// --- ???????? (????????????????��??????) ---
void adc_task(void)
{
    uint32_t adc_sum = 0;
    
    // 1. ???? DMA ???????????��?????????
    //    ??????????????????????????????????????
    for(uint16_t i = 0; i < ADC_DMA_BUFFER_SIZE; i++)
    {
        adc_sum += adc_dma_buffer[i];
    }
    
    // 2. ??????? ADC ?
    adc_val = adc_sum / ADC_DMA_BUFFER_SIZE; 
    
    // 3. (???) ????????????????????
    voltage = ((float)adc_val * 3.3f) / 4096.0f; // ????12��?????, 3.3V?��????

    // 4. ??��?????????? (adc_val ?? voltage)
    // my_printf(&huart1, "Average ADC: %lu, Voltage: %.2fV\n", adc_val, voltage);
}

#elif ADC_MODE == 3

#define BUFFER_SIZE 1000

extern DMA_HandleTypeDef hdma_adc1;

uint32_t dac_val_buffer[BUFFER_SIZE / 2];
__IO uint32_t adc_val_buffer[BUFFER_SIZE];

__IO uint8_t AdcConvEnd = 0;

void adc_tim_dma_init(void)
{
    HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
    __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);
}

void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef* hadc)
{
    UNUSED(hadc);
    if(hadc == &hadc1)
    {
        HAL_ADC_Stop_DMA(hadc);
        AdcConvEnd = 1;
    }
}

void adc_task(void)
{
    // ?????????? 3 + 12.5 =15.5??ADC???????
    // ?????????? 15.5 / 22.5 = 0.68us
    // 1000???????????? 1000 * 10 = 10000us = 10ms
    while(!AdcConvEnd);
    if(AdcConvEnd)
    {
        for(uint16_t i = 0; i < BUFFER_SIZE / 2; i++)
        {
            dac_val_buffer[i] = adc_val_buffer[i * 2 + 1];
        }
        for(uint16_t i = 0; i < BUFFER_SIZE / 2; i++)
        {
            my_printf(&huart1, "{dac}%d\r\n", (int)dac_val_buffer[i]);
        }
        memset(dac_val_buffer, 0, sizeof(uint32_t) * (BUFFER_SIZE / 2));
        HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_val_buffer, BUFFER_SIZE);
        __HAL_DMA_DISABLE_IT(&hdma_adc1, DMA_IT_HT);
        AdcConvEnd = 0;
    }
}

#endif
