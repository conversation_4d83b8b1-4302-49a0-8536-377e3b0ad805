/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dac.h"
#include "dma.h"
#include "i2c.h"
#include "spi.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "stdio.h"
#include "mydefine.h"
#include "oled.h"

#include "gd25qxx.h"
#include "lfs.h"
#include "lfs_port.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
lfs_t lfs;
struct lfs_config cfg;
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
#define TEST_BUFFER_SIZE    255
#define TEST_ADDRESS        0x000000

// Flash�����
#define CMD_REMS           0x90    // ��ȡ������ID/�豸ID
#define CMD_RDID           0x9F    // ��ȡʶ����Ϣ
#define CMD_READ_UNIQUE_ID 0x4B    // ��ȡΨһID

uint8_t write_buffer[TEST_BUFFER_SIZE];
uint8_t read_buffer[TEST_BUFFER_SIZE];

#define DUMMY_BYTE       0xA5

void spi_flash_read_id_by_rdid(uint8_t *manufacturer_id, uint8_t *memory_type, uint8_t *capacity)
{
    SPI_FLASH_CS_LOW();
    spi_flash_send_byte(CMD_RDID);
    *manufacturer_id = spi_flash_send_byte(DUMMY_BYTE);
    *memory_type = spi_flash_send_byte(DUMMY_BYTE);
    *capacity = spi_flash_send_byte(DUMMY_BYTE);
    SPI_FLASH_CS_HIGH();
}

void spi_flash_read_unique_id(uint8_t *unique_id)
{
    uint8_t i;
    SPI_FLASH_CS_LOW();
    spi_flash_send_byte(CMD_READ_UNIQUE_ID);
    // ����24λ��ַ 0x000000
    spi_flash_send_byte(0x00);
    spi_flash_send_byte(0x00);
    spi_flash_send_byte(0x00);
    // ����һ��dummy�ֽ�
    spi_flash_send_byte(DUMMY_BYTE);
    // ��ȡ16�ֽڵ�ΨһID
    for(i = 0; i < 16; i++) {
        unique_id[i] = spi_flash_send_byte(DUMMY_BYTE);
    }
    SPI_FLASH_CS_HIGH();
}
void spi_flash_read_id_by_rems(uint8_t *manufacturer_id, uint8_t *device_id)
{
    uint8_t dummy;
    
    SPI_FLASH_CS_LOW();
    HAL_Delay(1);
    
    spi_flash_send_byte(CMD_REMS);
    HAL_Delay(1);
    
    spi_flash_send_byte(0x00);
    spi_flash_send_byte(0x00);
    spi_flash_send_byte(0x00);
    HAL_Delay(1);
    dummy = spi_flash_send_byte(DUMMY_BYTE);
    HAL_Delay(1);
    *device_id = spi_flash_send_byte(DUMMY_BYTE);
    *manufacturer_id = spi_flash_send_byte(DUMMY_BYTE);
    
    SPI_FLASH_CS_HIGH();
}

void spi_flash_test_run(void)
{
    uint8_t manufacturer_id, device_id, memory_type, capacity;
    uint8_t unique_id[16];
    uint8_t i;
    
    // 1. ʹ��REMS�����ȡID
    my_printf(&huart1, "\r\nReading ID by REMS command (90H):\r\n");
    spi_flash_read_id_by_rems(&manufacturer_id, &device_id);
    my_printf(&huart1, "Manufacturer ID: 0x%02X\r\n", manufacturer_id);
    my_printf(&huart1, "Device ID: 0x%02X\r\n", device_id);
    HAL_Delay(10);
    
    // 2. ʹ��RDID�����ȡID
    my_printf(&huart1, "\r\nReading ID by RDID command (9FH):\r\n");
    spi_flash_read_id_by_rdid(&manufacturer_id, &memory_type, &capacity);
    my_printf(&huart1, "Manufacturer ID: 0x%02X\r\n", manufacturer_id);
    my_printf(&huart1, "Memory Type: 0x%02X\r\n", memory_type);
    my_printf(&huart1, "Capacity: 0x%02X\r\n", capacity);
    HAL_Delay(10);
    
    // 3. ��ȡΨһID
    my_printf(&huart1, "\r\nReading Unique ID by 4BH command:\r\n");
    spi_flash_read_unique_id(unique_id);
    my_printf(&huart1, "Unique ID: ");
    for(i = 0; i < 16; i++) {
        my_printf(&huart1, "%02X ", unique_id[i]);
    }
    my_printf(&huart1, "\r\n");
    HAL_Delay(10);
    
    // 4. ���Զ�д����
    my_printf(&huart1, "\r\nTesting read/write functions:\r\n");
    
    // ׼����������
    for(i = 0; i < TEST_BUFFER_SIZE; i++) {
        write_buffer[i] = i;
    }
    
    // ��������
    my_printf(&huart1, "Erasing sector...\r\n");
    spi_flash_sector_erase(TEST_ADDRESS);
    my_printf(&huart1, "Sector erase complete\r\n");
    HAL_Delay(10);
    
    // д������
    my_printf(&huart1, "Writing data...\r\n");
    spi_flash_buffer_write(write_buffer, TEST_ADDRESS, TEST_BUFFER_SIZE);
    my_printf(&huart1, "Write complete\r\n");
    HAL_Delay(10);
    
    // ��ȡ����
    my_printf(&huart1, "Reading data...\r\n");
    spi_flash_buffer_read(read_buffer, TEST_ADDRESS, TEST_BUFFER_SIZE);
    my_printf(&huart1, "Read complete\r\n");
    HAL_Delay(10);
    
    // ��֤����
    my_printf(&huart1, "Verifying data...\r\n");
    if(memcmp(write_buffer, read_buffer, TEST_BUFFER_SIZE) == 0) {
        my_printf(&huart1, "Data verification successful!\r\n");
    } else {
        my_printf(&huart1, "Data verification failed!\r\n");
        
        // ��ӡ��ƥ�������
        for(i = 0; i < TEST_BUFFER_SIZE; i++) {
            if(write_buffer[i] != read_buffer[i]) {
                my_printf(&huart1, "Mismatch at index %d: Write=0x%02X, Read=0x%02X\r\n", 
                       i, write_buffer[i], read_buffer[i]);
            }
        }
    }
    HAL_Delay(10);
    
    my_printf(&huart1, "\r\nSPI Flash Test Complete\r\n");
    HAL_Delay(100);
}

// LittleFS test function. Author: 米醋电子工作室
void lfs_basic_test(void) {
    my_printf(&huart1, "\r\n--- LittleFS Test ---\r\n");
    int err = lfs_mount(&lfs, &cfg);
    if (err) { // reformat if mount fails (e.g. first boot)
        my_printf(&huart1, "LFS: Mount fail(%d), formatting...\r\n", err);
        if (lfs_format(&lfs, &cfg) || (err = lfs_mount(&lfs, &cfg))) {
            my_printf(&huart1, "LFS: Format/Mount fail(%d)!\r\n", err); return;
        }
        my_printf(&huart1, "LFS: Format & Mount OK.\r\n");
    } else {
        my_printf(&huart1, "LFS: Mount OK.\r\n");
    }

    uint32_t boot_count = 0; lfs_file_t file;
    const char* filename = "boot_cnt.txt";
    err = lfs_file_open(&lfs, &file, filename, LFS_O_RDWR | LFS_O_CREAT);
    if (err) { my_printf(&huart1, "LFS: Open '%s' fail(%d)!\r\n", filename, err); goto end_test; }

    lfs_ssize_t r_sz = lfs_file_read(&lfs, &file, &boot_count, sizeof(boot_count));
    if (r_sz < 0) { // Read error
        my_printf(&huart1, "LFS: Read '%s' fail(%d). Initializing count.\r\n", filename, r_sz);
        boot_count = 0;
    } else if (r_sz != sizeof(boot_count)) { // Partial read or empty file
        my_printf(&huart1, "LFS: Read %ldb from '%s' (expected %db). Initializing count.\r\n", (long)r_sz, filename, (int)sizeof(boot_count));
        boot_count = 0;
    } // Else, successfully read previous count

    boot_count++; 
    my_printf(&huart1, "LFS: '%s' boot_count: %lu\r\n", filename, boot_count);

    err = lfs_file_rewind(&lfs, &file);
    if (err) { my_printf(&huart1, "LFS: Rewind '%s' fail(%d)!\r\n", filename, err); lfs_file_close(&lfs, &file); goto end_test; }
    
    lfs_ssize_t w_sz = lfs_file_write(&lfs, &file, &boot_count, sizeof(boot_count));
    if (w_sz < 0) { my_printf(&huart1, "LFS: Write '%s' fail(%d)!\r\n", filename, w_sz); }
    else if (w_sz != sizeof(boot_count)) { my_printf(&huart1, "LFS: Partial write to '%s' (%ld/%db)!\r\n", filename, (long)w_sz, (int)sizeof(boot_count)); }
    else { my_printf(&huart1, "LFS: '%s' updated.\r\n", filename); }

    if (lfs_file_close(&lfs, &file)) { my_printf(&huart1, "LFS: Close '%s' fail!\r\n", filename); }

end_test:
    my_printf(&huart1, "--- LittleFS Test End ---\r\n");
    // lfs_unmount(&lfs); // Optional: Unmount if needed, often not done if MCU resets.
}
/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_USART1_UART_Init();
  MX_ADC1_Init();
  MX_DAC_Init();
  MX_TIM3_Init();
  MX_TIM6_Init();
  MX_SPI2_Init();
  MX_USART2_UART_Init();
  MX_USART3_UART_Init();
  MX_I2C1_Init();
  /* USER CODE BEGIN 2 */
  app_btn_init();
  OLED_Init();
  scheduler_init();
//  dac_sin_init();
//  adc_tim_dma_init();

  my_printf(&huart1, "\r\n--- System Init OK ---\r\n");
  spi_flash_init(); // Ensure SPI Flash is ready
  my_printf(&huart1, "LFS: Initializing storage backend...\r\n");
  if (lfs_storage_init(&cfg) != LFS_ERR_OK) {
      my_printf(&huart1, "LFS: Storage backend init FAILED! Halting.\r\n");
      while(1);
  }
  my_printf(&huart1, "LFS: Storage backend init OK.\r\n");
  
  lfs_basic_test(); // Run LittleFS mount, format (if needed) and R/W test

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  //printf("hello"); // Original printf, can be removed or kept as is

  //spi_flash_test_run(); // Original Flash test, commented out as LFS test is more comprehensive for FS usage
  OLED_ShowString(0, 0, "LFS OK"); // Indicate LFS test completion on OLED
  OLED_Refresh_GRAM();  // 手动刷新显示
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
//    adc_task();
//    my_printf(&huart2, "hello");
//    my_printf(&huart3, "hello");
//    HAL_Delay(10);
    scheduler_run();
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 15;
  RCC_OscInitStruct.PLL.PLLN = 216;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Activate the Over-Drive mode
  */
  if (HAL_PWREx_EnableOverDrive() != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
