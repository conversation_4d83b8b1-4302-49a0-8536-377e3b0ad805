#include "stdio.h"
#include "string.h"
#include "stdarg.h"

#include "main.h"
#include "usart.h"
#include "math.h"
#include "adc.h"
#include "tim.h"
#include "dac.h"
#include "scheduler.h"

#include "adc_app.h"
#include "led_app.h"
#include "btn_app.h"
#include "usart_app.h"
#include "app_oled.h"

extern uint16_t uart_rx_index;
extern uint32_t uart_rx_ticks;
extern uint8_t uart_rx_buffer[128];
extern uint8_t uart_rx_dma_buffer[128];
extern UART_HandleTypeDef huart1;
extern DMA_HandleTypeDef hdma_usart1_rx;
